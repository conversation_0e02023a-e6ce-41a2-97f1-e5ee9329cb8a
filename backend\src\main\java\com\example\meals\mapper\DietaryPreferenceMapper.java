package com.example.meals.mapper;

import com.example.meals.entity.DietaryPreference;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 饮食偏好数据访问层
 */
@Mapper
public interface DietaryPreferenceMapper {

    /**
     * 查询所有饮食偏好
     */
    List<DietaryPreference> selectAll();

    /**
     * 查询启用的饮食偏好
     */
    List<DietaryPreference> selectEnabled();

    /**
     * 根据ID查询饮食偏好
     */
    DietaryPreference selectById(Long id);

    /**
     * 根据代码查询饮食偏好
     */
    DietaryPreference selectByCode(String code);

    /**
     * 插入饮食偏好
     */
    int insert(DietaryPreference dietaryPreference);

    /**
     * 更新饮食偏好
     */
    int update(DietaryPreference dietaryPreference);

    /**
     * 删除饮食偏好
     */
    int deleteById(Long id);

    /**
     * 检查代码是否存在（排除指定ID）
     */
    int countByCodeExcludeId(@Param("code") String code, @Param("excludeId") Long excludeId);

    /**
     * 检查代码是否存在
     */
    int countByCode(String code);

    /**
     * 获取最大排序号
     */
    int getMaxSortOrder();
}
