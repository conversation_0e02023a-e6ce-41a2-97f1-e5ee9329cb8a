package com.example.meals.service.impl;

import com.example.meals.common.Result;
import com.example.meals.config.FileUploadConfig;
import com.example.meals.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.UUID;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.ConcurrentHashMap;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;

/**
 * 文件服务实现类
 */
@Service
public class FileServiceImpl implements FileService {

    @Autowired
    private FileUploadConfig fileUploadConfig;

    // 用户级别的文件上传锁，防止并发上传冲突
    private final ConcurrentHashMap<Long, ReentrantLock> userLocks = new ConcurrentHashMap<>();
    
    @Override
    public Result<String> uploadAvatar(MultipartFile file, Long userId, String userType) {
        // 获取用户锁，防止并发上传
        ReentrantLock userLock = userLocks.computeIfAbsent(userId, k -> new ReentrantLock());

        try {
            userLock.lock();

            // 基本文件验证
            if (file == null || file.isEmpty()) {
                return Result.badRequest("请选择要上传的文件");
            }

            // 检查文件大小
            if (file.getSize() > fileUploadConfig.getMaxSize()) {
                return Result.badRequest("文件大小不能超过 " + (fileUploadConfig.getMaxSize() / 1024 / 1024) + "MB");
            }

            // 检查文件类型
            String originalFilename = file.getOriginalFilename();
            if (!StringUtils.hasText(originalFilename)) {
                return Result.badRequest("文件名不能为空");
            }

            String extension = getFileExtension(originalFilename).toLowerCase();
            boolean isAllowedType = Arrays.stream(fileUploadConfig.getAllowedTypes())
                    .anyMatch(type -> type.equalsIgnoreCase(extension));

            if (!isAllowedType) {
                return Result.badRequest("只支持以下格式的图片：" + String.join(", ", fileUploadConfig.getAllowedTypes()));
            }

            // 验证文件内容是否为真实图片
            Result<Void> contentValidation = validateImageContent(file);
            if (!contentValidation.getSuccess()) {
                return Result.badRequest(contentValidation.getMessage());
            }

            try {
                // 创建目录
                String avatarPath = fileUploadConfig.getAvatarPath();
                Path uploadPath = Paths.get(avatarPath);
                if (!Files.exists(uploadPath)) {
                    Files.createDirectories(uploadPath);
                }

                // 生成文件名
                String filename = generateFilename(userId, userType, extension);

                // 使用安全的文件保存方式
                Path filePath = uploadPath.resolve(filename);
                try (InputStream inputStream = file.getInputStream()) {
                    Files.copy(inputStream, filePath, StandardCopyOption.REPLACE_EXISTING);
                }

                // 返回完整的文件路径
                String fullPath = filePath.toString();
                return Result.success("头像上传成功", fullPath);

            } catch (IOException e) {
                return Result.error("文件上传失败：" + e.getMessage());
            }
        } finally {
            userLock.unlock();
        }
    }
    
    @Override
    public Result<Void> deleteAvatar(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return Result.success();
        }

        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                Files.delete(path);
            }
            return Result.success();
        } catch (IOException e) {
            return Result.error("删除文件失败：" + e.getMessage());
        }
    }
    
    @Override
    public String getFileUrl(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return null;
        }

        // 标准化路径分隔符，统一使用正斜杠
        String normalizedFilePath = filePath.replace("\\", "/");
        String normalizedConfigPath = fileUploadConfig.getPath().replace("\\", "/");

        // 如果是绝对路径，需要转换为相对路径用于URL访问
        if (normalizedFilePath.startsWith(normalizedConfigPath)) {
            // 提取相对于uploads目录的路径
            String relativePath = normalizedFilePath.substring(normalizedConfigPath.length());
            return "/api/files/uploads/" + relativePath;
        }

        // 兼容旧的相对路径格式
        return "/api/files/" + normalizedFilePath;
    }
    
    @Override
    public Result<Void> validateImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return Result.badRequest("请选择要上传的文件");
        }

        // 检查文件大小
        if (file.getSize() > fileUploadConfig.getMaxSize()) {
            return Result.badRequest("文件大小不能超过 " + (fileUploadConfig.getMaxSize() / 1024 / 1024) + "MB");
        }

        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFilename)) {
            return Result.badRequest("文件名不能为空");
        }

        String extension = getFileExtension(originalFilename).toLowerCase();
        boolean isAllowedType = Arrays.stream(fileUploadConfig.getAllowedTypes())
                .anyMatch(type -> type.equalsIgnoreCase(extension));

        if (!isAllowedType) {
            return Result.badRequest("只支持以下格式的图片：" + String.join(", ", fileUploadConfig.getAllowedTypes()));
        }

        return Result.success();
    }
    
    @Override
    public Result<Integer> cleanTempFiles() {
        // 清理临时文件的逻辑，这里暂时返回成功
        return Result.success("清理完成", 0);
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }
    
    /**
     * 验证图片文件内容的真实性
     */
    private Result<Void> validateImageContent(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            // 尝试读取图片，如果不是真实图片会抛出异常
            BufferedImage image = ImageIO.read(inputStream);
            if (image == null) {
                return Result.badRequest("文件不是有效的图片格式");
            }

            return Result.success();
        } catch (IOException e) {
            return Result.badRequest("图片文件损坏或格式不正确");
        }
    }

    /**
     * 生成文件名
     */
    private String generateFilename(Long userId, String userType, String extension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return String.format("%s_%d_%s_%s.%s", userType.toLowerCase(), userId, timestamp, uuid, extension);
    }
}
