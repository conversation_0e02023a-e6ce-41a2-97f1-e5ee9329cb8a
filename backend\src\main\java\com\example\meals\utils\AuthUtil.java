package com.example.meals.utils;

import com.example.meals.common.Result;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 权限验证工具类
 */
public class AuthUtil {
    
    /**
     * 验证管理员权限
     * 
     * @param request HTTP请求对象
     * @return 如果不是管理员，返回错误结果；否则返回null
     */
    public static Result<Void> checkAdminPermission(HttpServletRequest request) {
        String userType = (String) request.getAttribute("userType");
        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }
        return null;
    }
    
    /**
     * 验证用户权限（用户只能访问自己的资源，管理员可以访问任何资源）
     * 
     * @param request HTTP请求对象
     * @param targetUserId 目标用户ID
     * @return 如果权限不足，返回错误结果；否则返回null
     */
    public static Result<Void> checkUserPermission(HttpServletRequest request, Long targetUserId) {
        Long currentUserId = (Long) request.getAttribute("userId");
        String userType = (String) request.getAttribute("userType");
        
        // 管理员可以访问任何用户的资源
        if ("ADMIN".equals(userType)) {
            return null;
        }
        
        // 普通用户只能访问自己的资源
        if (targetUserId.equals(currentUserId)) {
            return null;
        }
        
        return Result.forbidden("权限不足，只能访问自己的资源");
    }
    
    /**
     * 获取当前用户ID
     * 
     * @param request HTTP请求对象
     * @return 当前用户ID
     */
    public static Long getCurrentUserId(HttpServletRequest request) {
        return (Long) request.getAttribute("userId");
    }
    
    /**
     * 获取当前用户类型
     * 
     * @param request HTTP请求对象
     * @return 当前用户类型
     */
    public static String getCurrentUserType(HttpServletRequest request) {
        return (String) request.getAttribute("userType");
    }
    
    /**
     * 检查是否为管理员
     * 
     * @param request HTTP请求对象
     * @return 是否为管理员
     */
    public static boolean isAdmin(HttpServletRequest request) {
        return "ADMIN".equals(getCurrentUserType(request));
    }
    
    /**
     * 检查是否为普通用户
     * 
     * @param request HTTP请求对象
     * @return 是否为普通用户
     */
    public static boolean isUser(HttpServletRequest request) {
        return "USER".equals(getCurrentUserType(request));
    }
}
