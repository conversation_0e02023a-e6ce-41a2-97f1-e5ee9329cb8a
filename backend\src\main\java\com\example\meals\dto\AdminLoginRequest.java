package com.example.meals.dto;

/**
 * 管理员登录请求DTO
 */
public class AdminLoginRequest {
    
    private String username; // 用户名
    private String password; // 密码
    
    // 构造函数
    public AdminLoginRequest() {}
    
    public AdminLoginRequest(String username, String password) {
        this.username = username;
        this.password = password;
    }
    
    // Getter 和 Setter 方法
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    @Override
    public String toString() {
        return "AdminLoginRequest{" +
                "username='" + username + '\'' +
                ", password='[PROTECTED]'" +
                '}';
    }
}
