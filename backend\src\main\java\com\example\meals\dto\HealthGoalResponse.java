package com.example.meals.dto;

import com.example.meals.entity.HealthGoal;
import java.time.LocalDateTime;

/**
 * 健康目标响应DTO
 */
public class HealthGoalResponse {
    
    private Long id;
    private String code;
    private String name;
    private String description;
    private Integer sortOrder;
    private Integer status;
    private String statusName;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    // 构造函数
    public HealthGoalResponse() {}
    
    public HealthGoalResponse(HealthGoal healthGoal) {
        this.id = healthGoal.getId();
        this.code = healthGoal.getCode();
        this.name = healthGoal.getName();
        this.description = healthGoal.getDescription();
        this.sortOrder = healthGoal.getSortOrder();
        this.status = healthGoal.getStatus();
        this.statusName = healthGoal.getStatus() == 1 ? "启用" : "禁用";
        this.createTime = healthGoal.getCreateTime();
        this.updateTime = healthGoal.getUpdateTime();
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
        this.statusName = status == 1 ? "启用" : "禁用";
    }
    
    public String getStatusName() {
        return statusName;
    }
    
    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    @Override
    public String toString() {
        return "HealthGoalResponse{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", sortOrder=" + sortOrder +
                ", status=" + status +
                ", statusName='" + statusName + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
