// 全局错误处理器

import router from '../router'

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  SERVER = 'SERVER',
  PERMISSION = 'PERMISSION',
  NOT_FOUND = 'NOT_FOUND',
  UNKNOWN = 'UNKNOWN'
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  type: ErrorType
  code?: number
  message: string
  details?: any
  timestamp: Date
}

/**
 * 全局错误处理器类
 */
export class GlobalErrorHandler {
  
  /**
   * 处理HTTP错误
   */
  static handleHttpError(error: any, response?: Response): void {
    const errorInfo: ErrorInfo = {
      type: ErrorType.UNKNOWN,
      message: '发生未知错误',
      timestamp: new Date()
    }

    if (response) {
      errorInfo.code = response.status
      
      switch (response.status) {
        case 403:
          errorInfo.type = ErrorType.PERMISSION
          errorInfo.message = '访问被拒绝，权限不足'
          router.push('/error/403')
          return
          
        case 404:
          errorInfo.type = ErrorType.NOT_FOUND
          errorInfo.message = '请求的资源不存在'
          router.push('/error/404')
          return
          
        case 500:
        case 502:
        case 503:
        case 504:
          errorInfo.type = ErrorType.SERVER
          errorInfo.message = '服务器内部错误'
          router.push('/error/500')
          return
          
        default:
          if (response.status >= 400 && response.status < 500) {
            errorInfo.type = ErrorType.PERMISSION
            errorInfo.message = '客户端请求错误'
          } else if (response.status >= 500) {
            errorInfo.type = ErrorType.SERVER
            errorInfo.message = '服务器错误'
            router.push('/error/500')
            return
          }
      }
    } else if (error) {
      // 网络错误或其他错误
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        errorInfo.type = ErrorType.NETWORK
        errorInfo.message = '网络连接失败'
        router.push('/error/network')
        return
      } else if (error.name === 'AbortError') {
        errorInfo.type = ErrorType.NETWORK
        errorInfo.message = '请求超时'
        router.push('/error/network')
        return
      }
    }

    // 记录错误日志
    this.logError(errorInfo)
  }

  /**
   * 处理网络错误
   */
  static handleNetworkError(error: any): void {
    const errorInfo: ErrorInfo = {
      type: ErrorType.NETWORK,
      message: '网络连接失败',
      details: error,
      timestamp: new Date()
    }

    this.logError(errorInfo)
    router.push('/error/network')
  }

  /**
   * 处理权限错误
   */
  static handlePermissionError(message?: string): void {
    const errorInfo: ErrorInfo = {
      type: ErrorType.PERMISSION,
      code: 403,
      message: message || '访问被拒绝',
      timestamp: new Date()
    }

    this.logError(errorInfo)
    router.push('/error/403')
  }

  /**
   * 处理服务器错误
   */
  static handleServerError(code?: number, message?: string): void {
    const errorInfo: ErrorInfo = {
      type: ErrorType.SERVER,
      code: code || 500,
      message: message || '服务器内部错误',
      timestamp: new Date()
    }

    this.logError(errorInfo)
    router.push('/error/500')
  }

  /**
   * 记录错误日志
   */
  private static logError(errorInfo: ErrorInfo): void {
    console.error('[GlobalErrorHandler]', errorInfo)
    
    // 这里可以添加错误上报逻辑
    // 例如发送到错误监控服务
    this.reportError(errorInfo)
  }

  /**
   * 上报错误到监控服务
   */
  private static reportError(errorInfo: ErrorInfo): void {
    try {
      // 这里可以集成错误监控服务，如 Sentry、Bugsnag 等
      // 示例：发送错误报告到后端
      const errorReport = {
        type: errorInfo.type,
        code: errorInfo.code,
        message: errorInfo.message,
        details: errorInfo.details,
        timestamp: errorInfo.timestamp.toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: this.getCurrentUserId()
      }

      // 异步发送错误报告，不阻塞用户操作
      fetch('/api/error-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorReport)
      }).catch(() => {
        // 错误上报失败时静默处理
      })
    } catch (error) {
      // 错误上报过程中出错时静默处理
    }
  }

  /**
   * 获取当前用户ID
   */
  private static getCurrentUserId(): string | null {
    try {
      const userStr = localStorage.getItem('user_info')
      if (userStr) {
        const user = JSON.parse(userStr)
        return user.id || null
      }
    } catch (error) {
      // 解析用户信息失败时返回null
    }
    return null
  }
}

/**
 * 增强的fetch函数，自动处理错误
 */
export async function enhancedFetch(url: string, options: RequestInit = {}): Promise<Response> {
  try {
    const response = await fetch(url, options)
    
    if (!response.ok) {
      GlobalErrorHandler.handleHttpError(null, response)
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    return response
  } catch (error) {
    if (error instanceof TypeError && error.message.includes('fetch')) {
      GlobalErrorHandler.handleNetworkError(error)
    } else if (error && typeof error === 'object' && 'name' in error && error.name === 'AbortError') {
      GlobalErrorHandler.handleNetworkError(error)
    }
    throw error
  }
}

/**
 * 全局未捕获错误处理
 */
export function setupGlobalErrorHandling(): void {
  // 捕获未处理的Promise拒绝
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    // 检查是否为网络错误
    if (event.reason && typeof event.reason === 'object') {
      if (event.reason.name === 'TypeError' && event.reason.message.includes('fetch')) {
        GlobalErrorHandler.handleNetworkError(event.reason)
        event.preventDefault() // 阻止默认的错误处理
        return
      }
    }
    
    // 其他未处理的错误
    GlobalErrorHandler.handleHttpError(event.reason)
    event.preventDefault()
  })

  // 捕获JavaScript运行时错误
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error)
    
    const errorInfo: ErrorInfo = {
      type: ErrorType.UNKNOWN,
      message: event.message || '发生未知错误',
      details: {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      },
      timestamp: new Date()
    }
    
    GlobalErrorHandler['logError'](errorInfo)
  })
}

/**
 * 检查网络连接状态
 */
export function checkNetworkStatus(): boolean {
  return navigator.onLine
}

/**
 * 监听网络状态变化
 */
export function setupNetworkStatusMonitoring(): void {
  window.addEventListener('online', () => {
    console.log('Network connection restored')
    // 可以在这里添加网络恢复后的处理逻辑
  })

  window.addEventListener('offline', () => {
    console.log('Network connection lost')
    // 网络断开时可以显示提示或重定向到网络错误页面
    GlobalErrorHandler.handleNetworkError(new Error('Network connection lost'))
  })
}

// 导出便捷方法
export const errorHandler = GlobalErrorHandler
