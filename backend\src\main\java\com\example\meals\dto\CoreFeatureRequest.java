package com.example.meals.dto;

import java.util.List;

/**
 * 核心功能请求DTO
 */
public class CoreFeatureRequest {
    
    private String title;
    private String description;
    private String icon;
    private List<String> highlights;
    private Integer sortOrder;
    private Integer status;
    
    // 构造函数
    public CoreFeatureRequest() {}
    
    public CoreFeatureRequest(String title, String description, String icon, List<String> highlights, Integer sortOrder, Integer status) {
        this.title = title;
        this.description = description;
        this.icon = icon;
        this.highlights = highlights;
        this.sortOrder = sortOrder;
        this.status = status;
    }
    
    // Getter和Setter方法
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getIcon() {
        return icon;
    }
    
    public void setIcon(String icon) {
        this.icon = icon;
    }
    
    public List<String> getHighlights() {
        return highlights;
    }
    
    public void setHighlights(List<String> highlights) {
        this.highlights = highlights;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "CoreFeatureRequest{" +
                "title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", icon='" + icon + '\'' +
                ", highlights=" + highlights +
                ", sortOrder=" + sortOrder +
                ", status=" + status +
                '}';
    }
}
