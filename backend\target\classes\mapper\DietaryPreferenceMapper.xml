<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.DietaryPreferenceMapper">

    <!-- 结果映射 -->
    <resultMap id="DietaryPreferenceResultMap" type="com.example.meals.entity.DietaryPreference">
        <id property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 查询所有饮食偏好 -->
    <select id="selectAll" resultMap="DietaryPreferenceResultMap">
        SELECT * FROM dietary_preference 
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 查询启用的饮食偏好 -->
    <select id="selectEnabled" resultMap="DietaryPreferenceResultMap">
        SELECT * FROM dietary_preference 
        WHERE status = 1 
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据ID查询饮食偏好 -->
    <select id="selectById" parameterType="long" resultMap="DietaryPreferenceResultMap">
        SELECT * FROM dietary_preference WHERE id = #{id}
    </select>

    <!-- 根据代码查询饮食偏好 -->
    <select id="selectByCode" parameterType="string" resultMap="DietaryPreferenceResultMap">
        SELECT * FROM dietary_preference WHERE code = #{code}
    </select>

    <!-- 插入饮食偏好 -->
    <insert id="insert" parameterType="com.example.meals.entity.DietaryPreference" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO dietary_preference (code, name, description, sort_order, status)
        VALUES (#{code}, #{name}, #{description}, #{sortOrder}, #{status})
    </insert>

    <!-- 更新饮食偏好 -->
    <update id="update" parameterType="com.example.meals.entity.DietaryPreference">
        UPDATE dietary_preference SET
            code = #{code},
            name = #{name},
            description = #{description},
            sort_order = #{sortOrder},
            status = #{status}
        WHERE id = #{id}
    </update>

    <!-- 删除饮食偏好 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM dietary_preference WHERE id = #{id}
    </delete>

    <!-- 检查代码是否存在（排除指定ID） -->
    <select id="countByCodeExcludeId" resultType="int">
        SELECT COUNT(*) FROM dietary_preference 
        WHERE code = #{code} AND id != #{excludeId}
    </select>

    <!-- 检查代码是否存在 -->
    <select id="countByCode" parameterType="string" resultType="int">
        SELECT COUNT(*) FROM dietary_preference WHERE code = #{code}
    </select>

    <!-- 获取最大排序号 -->
    <select id="getMaxSortOrder" resultType="int">
        SELECT COALESCE(MAX(sort_order), 0) FROM dietary_preference
    </select>

</mapper>
