package com.example.meals.mapper;

import com.example.meals.entity.EmailVerification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 邮箱验证码Mapper接口
 */
@Mapper
public interface EmailVerificationMapper {

    /**
     * 插入验证码记录
     */
    int insert(EmailVerification emailVerification);

    /**
     * 根据邮箱和类型查找最新的有效验证码
     */
    EmailVerification findLatestValidCode(@Param("email") String email, @Param("type") String type);

    /**
     * 根据邮箱、验证码和类型查找验证码记录
     */
    EmailVerification findByEmailAndCode(@Param("email") String email,
                                       @Param("code") String code,
                                       @Param("type") String type);

    /**
     * 更新验证码状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status, @Param("useTime") LocalDateTime useTime);

    /**
     * 使验证码失效（将同一邮箱同一类型的所有未使用验证码标记为已过期）
     */
    int invalidateCodesByEmailAndType(@Param("email") String email, @Param("type") String type);

    /**
     * 清理过期的验证码记录
     */
    int deleteExpiredCodes(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计指定邮箱在指定时间内发送的验证码数量
     */
    int countRecentCodes(@Param("email") String email,
                        @Param("type") String type,
                        @Param("afterTime") LocalDateTime afterTime);

    /**
     * 根据ID查找验证码记录
     */
    EmailVerification findById(@Param("id") Long id);

    /**
     * 查找指定邮箱的所有验证码记录
     */
    List<EmailVerification> findByEmail(@Param("email") String email);
}
