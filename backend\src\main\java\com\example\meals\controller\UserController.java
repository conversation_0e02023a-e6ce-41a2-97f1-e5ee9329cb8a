package com.example.meals.controller;

import com.example.meals.dto.LoginRequest;
import com.example.meals.dto.EmailCodeLoginRequest;
import com.example.meals.dto.RegisterRequest;
import com.example.meals.dto.UserResponse;
import com.example.meals.dto.request.ResetPasswordRequest;
import com.example.meals.dto.request.UpdateProfileRequest;
import com.example.meals.service.UserService;
import com.example.meals.service.EmailVerificationService;
import com.example.meals.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api/user")
public class UserController {
    
    @Autowired
    private UserService userService;

    @Autowired
    private EmailVerificationService emailVerificationService;
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<UserResponse> register(@RequestBody RegisterRequest request) {
        return userService.register(request);
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<UserResponse> login(@RequestBody LoginRequest request) {
        return userService.login(request);
    }

    /**
     * 邮箱验证码登录
     */
    @PostMapping("/login-with-code")
    public Result<UserResponse> loginWithCode(@RequestBody EmailCodeLoginRequest request) {
        return userService.loginWithEmailCode(request);
    }
    
    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        return userService.checkUsername(username);
    }
    
    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(@RequestParam String email) {
        return userService.checkEmail(email);
    }

    /**
     * 发送邮箱验证码
     */
    @PostMapping("/send-verification-code")
    public Result<Void> sendVerificationCode(@RequestParam String email,
                                           @RequestParam(defaultValue = "REGISTER") String type) {
        return emailVerificationService.sendVerificationCode(email, type);
    }

    /**
     * 验证邮箱验证码
     */
    @PostMapping("/verify-code")
    public Result<Void> verifyCode(@RequestParam String email,
                                 @RequestParam String code,
                                 @RequestParam(defaultValue = "REGISTER") String type) {
        return emailVerificationService.verifyCode(email, code, type);
    }

    /**
     * 检查验证码是否有效（不消费验证码）
     */
    @GetMapping("/check-verification-code")
    public Result<Boolean> checkVerificationCode(@RequestParam String email,
                                                @RequestParam String code,
                                                @RequestParam(defaultValue = "REGISTER") String type) {
        return emailVerificationService.checkCode(email, code, type);
    }

    /**
     * 重置密码
     */
    @PostMapping("/reset-password")
    public Result<Void> resetPassword(@RequestBody ResetPasswordRequest request) {
        return userService.resetPassword(request);
    }

    /**
     * 根据ID获取用户信息
     */
    @GetMapping("/{id}")
    public Result<UserResponse> getUserById(@PathVariable Long id, HttpServletRequest request) {
        // 验证用户权限：只能查看自己的信息，或管理员可以查看任何用户信息
        Long currentUserId = (Long) request.getAttribute("userId");
        String userType = (String) request.getAttribute("userType");

        if (!"ADMIN".equals(userType) && !id.equals(currentUserId)) {
            return Result.forbidden("权限不足，只能查看自己的信息");
        }

        return userService.getUserById(id);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    public Result<UserResponse> getCurrentUserProfile(HttpServletRequest request) {
        Long currentUserId = (Long) request.getAttribute("userId");
        return userService.getCurrentUserInfo(currentUserId);
    }

    /**
     * 更新用户资料
     */
    @PutMapping("/profile")
    public Result<UserResponse> updateProfile(@RequestBody UpdateProfileRequest updateRequest,
                                            HttpServletRequest request) {
        Long currentUserId = (Long) request.getAttribute("userId");
        return userService.updateProfile(currentUserId, updateRequest);
    }
}
