package com.example.meals.service;

import com.example.meals.common.Result;
import com.example.meals.dto.DietaryPreferenceResponse;
import com.example.meals.dto.request.DietaryPreferenceRequest;

import java.util.List;

/**
 * 饮食偏好服务接口
 */
public interface DietaryPreferenceService {
    
    /**
     * 获取所有饮食偏好
     */
    Result<List<DietaryPreferenceResponse>> getAllDietaryPreferences();
    
    /**
     * 获取启用的饮食偏好
     */
    Result<List<DietaryPreferenceResponse>> getEnabledDietaryPreferences();
    
    /**
     * 根据ID获取饮食偏好
     */
    Result<DietaryPreferenceResponse> getDietaryPreferenceById(Long id);
    
    /**
     * 创建饮食偏好
     */
    Result<DietaryPreferenceResponse> createDietaryPreference(DietaryPreferenceRequest request);
    
    /**
     * 更新饮食偏好
     */
    Result<DietaryPreferenceResponse> updateDietaryPreference(Long id, DietaryPreferenceRequest request);
    
    /**
     * 删除饮食偏好
     */
    Result<Void> deleteDietaryPreference(Long id);
}
