<template>
  <div class="forgot-password-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <div class="forgot-password-content">
      <!-- 返回登录链接 -->
      <div class="back-to-login">
        <router-link to="/login" class="back-link">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          返回登录
        </router-link>
      </div>

      <div class="forgot-password-card">
        <div class="card-header">
          <div class="logo">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                    fill="currentColor"/>
              <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <h1 class="card-title">重置密码</h1>
          <p class="card-subtitle">请输入您的邮箱地址，我们将发送验证码帮助您重置密码</p>
        </div>

        <!-- 全局错误提示 -->
        <div v-if="globalError" class="error-alert">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
            <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
          </svg>
          <span>{{ globalError }}</span>
        </div>

        <!-- 成功提示 -->
        <div v-if="showSuccess" class="success-alert">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M22 11.08V12A10 10 0 1 1 5.93 7.25" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <polyline points="22,4 12,14.01 9,11.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span>{{ successMessage }}</span>
        </div>

        <form @submit.prevent="handleSubmit" class="forgot-password-form">
          <!-- 步骤1: 输入邮箱 -->
          <template v-if="currentStep === 1">
            <div class="form-group">
              <label for="email" class="form-label">邮箱地址</label>
              <div class="input-wrapper">
                <input
                  id="email"
                  v-model="resetForm.email"
                  type="email"
                  class="form-input"
                  :class="{ 'error': errors.email }"
                  placeholder="请输入您的邮箱地址"
                  @blur="validateEmail"
                />
                <svg class="input-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <span v-if="errors.email" class="error-message">{{ errors.email }}</span>
            </div>

            <button type="submit" class="submit-button" :disabled="isLoading">
              <span v-if="isLoading" class="loading-spinner"></span>
              <span>{{ isLoading ? '发送中...' : '发送验证码' }}</span>
            </button>
          </template>

          <!-- 步骤2: 验证码和新密码 -->
          <template v-else-if="currentStep === 2">
            <div class="step-info">
              <p>验证码已发送至 <strong>{{ resetForm.email }}</strong></p>
              <button type="button" @click="goBackToStep1" class="change-email-btn">更换邮箱</button>
            </div>

            <div class="form-group">
              <label for="verificationCode" class="form-label">验证码</label>
              <div class="input-wrapper">
                <input
                  id="verificationCode"
                  v-model="resetForm.verificationCode"
                  type="text"
                  class="form-input"
                  :class="{ 'error': errors.verificationCode }"
                  placeholder="请输入6位验证码"
                  maxlength="6"
                  @blur="validateVerificationCode"
                />
                <svg class="input-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="16" r="1" fill="currentColor"/>
                  <path d="M7 11V7A5 5 0 0 1 17 7V11" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
              <span v-if="errors.verificationCode" class="error-message">{{ errors.verificationCode }}</span>
            </div>

            <div class="form-group">
              <label for="newPassword" class="form-label">新密码</label>
              <div class="input-wrapper">
                <input
                  id="newPassword"
                  v-model="resetForm.newPassword"
                  :type="showPassword ? 'text' : 'password'"
                  class="form-input"
                  :class="{ 'error': errors.newPassword }"
                  placeholder="请输入新密码（至少6位）"
                  @blur="validateNewPassword"
                />
                <button
                  type="button"
                  class="password-toggle"
                  @click="showPassword = !showPassword"
                >
                  <svg v-if="showPassword" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42" 
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M1 1L23 23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M10.584 10.587A2 2 0 0 0 13.415 13.414" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" 
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
              </div>
              <span v-if="errors.newPassword" class="error-message">{{ errors.newPassword }}</span>
            </div>

            <div class="form-group">
              <label for="confirmPassword" class="form-label">确认新密码</label>
              <div class="input-wrapper">
                <input
                  id="confirmPassword"
                  v-model="confirmPassword"
                  :type="showPassword ? 'text' : 'password'"
                  class="form-input"
                  :class="{ 'error': errors.confirmPassword }"
                  placeholder="请再次输入新密码"
                  @blur="validateConfirmPassword"
                />
                <button
                  type="button"
                  class="password-toggle"
                  @click="showPassword = !showPassword"
                >
                  <svg v-if="showPassword" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42"
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M1 1L23 23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M10.584 10.587A2 2 0 0 0 13.415 13.414" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z"
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
              </div>
              <span v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</span>
            </div>

            <div class="resend-section">
              <button 
                type="button" 
                @click="resendCode" 
                :disabled="countdown > 0 || isCodeLoading"
                class="resend-btn"
              >
                {{ countdown > 0 ? `${countdown}秒后重发` : '重新发送验证码' }}
              </button>
            </div>

            <button type="submit" class="submit-button" :disabled="isLoading">
              <span v-if="isLoading" class="loading-spinner"></span>
              <span>{{ isLoading ? '重置中...' : '重置密码' }}</span>
            </button>
          </template>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { sendVerificationCode, resetPassword, type ResetPasswordRequest, type ApiResponse } from '../utils/userApi'
import { message } from '../utils/message'

// 路由
const router = useRouter()

// 响应式数据
const currentStep = ref(1)
const showPassword = ref(false)
const isLoading = ref(false)
const isCodeLoading = ref(false)
const globalError = ref('')
const successMessage = ref('')
const showSuccess = ref(false)
const countdown = ref(0)
const countdownTimer = ref<number | null>(null)
const confirmPassword = ref('')

// 表单数据
const resetForm = reactive<ResetPasswordRequest>({
  email: '',
  verificationCode: '',
  newPassword: ''
})

// 表单错误
const errors = reactive({
  email: '',
  verificationCode: '',
  newPassword: '',
  confirmPassword: ''
})

// 验证邮箱
const validateEmail = () => {
  const emailPattern = /^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/
  if (!resetForm.email) {
    errors.email = '请输入邮箱地址'
  } else if (!emailPattern.test(resetForm.email)) {
    errors.email = '邮箱格式不正确'
  } else {
    errors.email = ''
  }
}

// 验证验证码
const validateVerificationCode = () => {
  if (!resetForm.verificationCode) {
    errors.verificationCode = '请输入验证码'
  } else if (resetForm.verificationCode.length !== 6) {
    errors.verificationCode = '验证码应为6位数字'
  } else {
    errors.verificationCode = ''
  }
}

// 验证新密码
const validateNewPassword = () => {
  if (!resetForm.newPassword) {
    errors.newPassword = '请输入新密码'
  } else if (resetForm.newPassword.length < 6) {
    errors.newPassword = '密码长度至少6位'
  } else {
    errors.newPassword = ''
  }
}

// 验证确认密码
const validateConfirmPassword = () => {
  if (!confirmPassword.value) {
    errors.confirmPassword = '请确认新密码'
  } else if (confirmPassword.value !== resetForm.newPassword) {
    errors.confirmPassword = '两次输入的密码不一致'
  } else {
    errors.confirmPassword = ''
  }
}

// 发送验证码
const sendCode = async () => {
  validateEmail()
  if (errors.email) return

  isLoading.value = true
  globalError.value = ''

  try {
    const response = await sendVerificationCode(resetForm.email, 'forgot_password')
    if (response.success) {
      currentStep.value = 2
      startCountdown()
      message.success('验证码已发送，请查收邮件')
    } else {
      // 特殊处理邮箱未注册的情况
      if (response.message === '邮箱未注册') {
        errors.email = '该邮箱尚未注册，请先注册账号'
        globalError.value = ''
      } else {
        globalError.value = response.message || '发送验证码失败'
      }
    }
  } catch (error) {
    globalError.value = '发送验证码失败，请重试'
  } finally {
    isLoading.value = false
  }
}

// 重新发送验证码
const resendCode = async () => {
  isCodeLoading.value = true
  try {
    const response = await sendVerificationCode(resetForm.email, 'forgot_password')
    if (response.success) {
      startCountdown()
      message.success('验证码已重新发送')
    } else {
      message.error(response.message || '发送验证码失败')
    }
  } catch (error) {
    message.error('发送验证码失败，请重试')
  } finally {
    isCodeLoading.value = false
  }
}

// 重置密码
const resetUserPassword = async () => {
  // 验证所有字段
  validateVerificationCode()
  validateNewPassword()
  validateConfirmPassword()

  if (errors.verificationCode || errors.newPassword || errors.confirmPassword) {
    return
  }

  isLoading.value = true
  globalError.value = ''

  try {
    const response = await resetPassword(resetForm)
    if (response.success) {
      showSuccess.value = true
      successMessage.value = '密码重置成功！即将跳转到登录页面...'
      
      // 3秒后跳转到登录页面
      setTimeout(() => {
        router.push('/login')
      }, 3000)
    } else {
      globalError.value = response.message || '密码重置失败'
    }
  } catch (error) {
    globalError.value = '密码重置失败，请重试'
  } finally {
    isLoading.value = false
  }
}

// 处理表单提交
const handleSubmit = () => {
  if (currentStep.value === 1) {
    sendCode()
  } else {
    resetUserPassword()
  }
}

// 返回第一步
const goBackToStep1 = () => {
  currentStep.value = 1
  resetForm.verificationCode = ''
  resetForm.newPassword = ''
  confirmPassword.value = ''
  clearErrors()
}

// 清除错误
const clearErrors = () => {
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })
  globalError.value = ''
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  countdownTimer.value = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value!)
      countdownTimer.value = null
    }
  }, 1000)
}

// 清理定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})
</script>

<style scoped>
/* CSS变量定义 - 与登录页面保持一致 */
:root {
  /* 主色调 - 自然绿色系 */
  --primary-green: #16a085;
  --primary-green-light: #48c9b0;
  --primary-green-dark: #138d75;
  --primary-green-bg: rgba(22, 160, 133, 0.08);

  /* 中性色系 */
  --text-primary: #1a252f;
  --text-secondary: #2c3e50;
  --text-light: #34495e;
  --text-muted: #5d6d7e;
  --text-placeholder: #6c757d;

  /* 背景色系 */
  --background-white: #ffffff;
  --background-gray: #f8f9fa;
  --background-light: #ecf0f1;

  /* 边框色系 */
  --border-light: #bdc3c7;
  --border-medium: #95a5a6;
  --border-focus: var(--primary-green);

  /* 状态色系 */
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --error-color: #ff4444;

  /* 阴影 */
  --shadow-sm: 0 1px 3px 0 rgba(44, 62, 80, 0.08);
  --shadow-md: 0 4px 12px 0 rgba(44, 62, 80, 0.12);
  --shadow-lg: 0 8px 25px 0 rgba(44, 62, 80, 0.15);
  --shadow-xl: 0 12px 35px 0 rgba(44, 62, 80, 0.18);

  /* 圆角 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* 主容器 */
.forgot-password-container {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #f8f9fa 0%, #ecf0f1 30%, #e8f6f3 70%, #d5f4e6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.08), rgba(72, 201, 176, 0.04));
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
  animation-delay: 0s;
}

.circle-2 {
  width: 250px;
  height: 250px;
  bottom: -125px;
  left: -125px;
  animation-delay: 3s;
}

.circle-3 {
  width: 200px;
  height: 200px;
  top: 50%;
  left: -100px;
  animation-delay: 1.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-30px) scale(1.05);
    opacity: 0.8;
  }
}

/* 主要内容 */
.forgot-password-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 420px;
}

/* 返回登录链接 */
.back-to-login {
  margin-bottom: 1.5rem;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #5d6d7e;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  padding: 0.5rem 0;
}

.back-link:hover {
  color: #16a085;
  transform: translateX(-2px);
}

.back-link svg {
  width: 1rem;
  height: 1rem;
}

/* 卡片样式 */
.forgot-password-card {
  background: #ffffff;
  border-radius: 1rem;
  padding: 2.5rem;
  box-shadow: 0 12px 35px 0 rgba(44, 62, 80, 0.18);
  border: 1px solid rgba(22, 160, 133, 0.08);
  backdrop-filter: blur(10px);
}

/* 卡片头部 */
.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.logo {
  width: 3.5rem;
  height: 3.5rem;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #16a085 0%, #48c9b0 100%);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px 0 rgba(44, 62, 80, 0.12);
}

.logo svg {
  width: 1.75rem;
  height: 1.75rem;
}

.card-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1a252f;
  margin: 0 0 0.75rem 0;
  letter-spacing: -0.025em;
}

.card-subtitle {
  color: #5d6d7e;
  font-size: 0.875rem;
  line-height: 1.6;
  margin: 0;
}

/* 表单样式 */
.forgot-password-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  padding-right: 3rem;
  border: 2px solid #e8f4f8;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: #ffffff;
  box-sizing: border-box;
  color: #1a252f;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-input::placeholder {
  color: #6c757d;
  opacity: 1;
}

.form-input:hover {
  border-color: #d5e8e3;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.form-input:focus {
  outline: none;
  border-color: #16a085;
  box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.12), 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-input.error {
  border-color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.05);
}

.form-input.error:focus {
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.15);
  border-color: #e74c3c;
}

.input-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: #6c757d;
  pointer-events: none;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  transition: color 0.2s ease;
}

.password-toggle:hover {
  color: #5d6d7e;
}

/* 隐藏浏览器默认的密码显示切换按钮 */
input[type="password"]::-ms-reveal,
input[type="password"]::-webkit-credentials-auto-fill-button {
  display: none;
}

.error-message {
  color: #ff4444;
  font-size: 0.75rem;
  font-weight: 500;
  margin-top: 0.25rem;
}

/* 步骤信息 */
.step-info {
  background: rgba(22, 160, 133, 0.08);
  border: 1px solid rgba(22, 160, 133, 0.2);
  border-radius: 0.5rem;
  padding: 1rem;
  text-align: center;
}

.step-info p {
  margin: 0 0 0.5rem 0;
  color: #138d75;
  font-size: 0.875rem;
}

.change-email-btn {
  background: none;
  border: none;
  color: #16a085;
  font-size: 0.75rem;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.change-email-btn:hover {
  color: #138d75;
}

/* 重发验证码 */
.resend-section {
  text-align: center;
}

.resend-btn {
  background: none;
  border: none;
  color: #16a085;
  font-size: 0.875rem;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
  transition: color 0.2s ease;
}

.resend-btn:hover:not(:disabled) {
  color: #138d75;
}

.resend-btn:disabled {
  color: #6c757d;
  cursor: not-allowed;
  text-decoration: none;
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, #16a085 0%, #48c9b0 100%);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 3rem;
  box-shadow: 0 1px 3px 0 rgba(44, 62, 80, 0.08);
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px 0 rgba(44, 62, 80, 0.12);
  background: linear-gradient(135deg, #138d75 0%, #16a085 100%);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 1px 3px 0 rgba(44, 62, 80, 0.08);
}

/* 加载动画 */
.loading-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 警告提示 */
.error-alert, .success-alert {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
}

.error-alert {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #ff4444;
}

.success-alert {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #27ae60;
}

.error-alert svg, .success-alert svg {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .forgot-password-container {
    padding: 1rem;
  }

  .forgot-password-card {
    padding: 2rem;
  }

  .card-title {
    font-size: 1.5rem;
  }

  .logo {
    width: 3rem;
    height: 3rem;
  }
}

@media (max-width: 480px) {
  .forgot-password-card {
    padding: 1.5rem;
  }

  .card-title {
    font-size: 1.375rem;
  }
}
</style>
