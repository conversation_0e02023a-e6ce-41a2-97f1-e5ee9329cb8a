package com.example.meals.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 核心功能实体类
 */
public class CoreFeature {
    
    private Long id;
    private String title;
    private String description;
    private String icon;
    private String highlightsJson; // 数据库中的JSON字符串
    private List<String> highlights; // 转换后的列表
    private Integer sortOrder;
    private Integer status;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    // 静态ObjectMapper实例
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    // 构造函数
    public CoreFeature() {}
    
    public CoreFeature(String title, String description, String icon, List<String> highlights, Integer sortOrder, Integer status) {
        this.title = title;
        this.description = description;
        this.icon = icon;
        this.highlights = highlights;
        this.sortOrder = sortOrder;
        this.status = status;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getIcon() {
        return icon;
    }
    
    public void setIcon(String icon) {
        this.icon = icon;
    }
    
    public String getHighlightsJson() {
        return highlightsJson;
    }
    
    public void setHighlightsJson(String highlightsJson) {
        this.highlightsJson = highlightsJson;
        // 当设置JSON字符串时，自动转换为List
        if (highlightsJson != null && !highlightsJson.isEmpty()) {
            try {
                this.highlights = objectMapper.readValue(highlightsJson, new TypeReference<List<String>>() {});
            } catch (JsonProcessingException e) {
                e.printStackTrace();
                this.highlights = List.of(); // 如果解析失败，设置为空列表
            }
        }
    }
    
    public List<String> getHighlights() {
        return highlights;
    }
    
    public void setHighlights(List<String> highlights) {
        this.highlights = highlights;
        // 当设置List时，自动转换为JSON字符串
        if (highlights != null) {
            try {
                this.highlightsJson = objectMapper.writeValueAsString(highlights);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
                this.highlightsJson = "[]"; // 如果转换失败，设置为空数组
            }
        }
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    // 状态名称
    public String getStatusName() {
        return status != null && status == 1 ? "启用" : "禁用";
    }
    
    @Override
    public String toString() {
        return "CoreFeature{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", icon='" + icon + '\'' +
                ", highlights=" + highlights +
                ", sortOrder=" + sortOrder +
                ", status=" + status +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
