// 简单的消息提示工具

export interface MessageOptions {
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  position?: 'top' | 'center' | 'bottom'
}

class MessageManager {
  private container: HTMLElement | null = null
  
  private createContainer() {
    if (this.container) return this.container
    
    this.container = document.createElement('div')
    this.container.className = 'message-container'
    this.container.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 9999;
      pointer-events: none;
    `
    document.body.appendChild(this.container)
    return this.container
  }
  
  private createMessage(text: string, options: MessageOptions = {}) {
    const {
      type = 'info',
      duration = 3000,
      position = 'top'
    } = options
    
    const messageEl = document.createElement('div')
    messageEl.className = `message message-${type}`
    messageEl.textContent = text
    
    // 样式
    const baseStyle = `
      padding: 12px 20px;
      margin-bottom: 10px;
      border-radius: 6px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      opacity: 0;
      transform: translateY(-20px);
      transition: all 0.3s ease;
      pointer-events: auto;
      max-width: 400px;
      word-wrap: break-word;
    `
    
    const typeStyles = {
      success: 'background-color: #10b981;',
      error: 'background-color: #ef4444;',
      warning: 'background-color: #f59e0b;',
      info: 'background-color: #3b82f6;'
    }
    
    messageEl.style.cssText = baseStyle + typeStyles[type]
    
    const container = this.createContainer()
    container.appendChild(messageEl)
    
    // 动画显示
    requestAnimationFrame(() => {
      messageEl.style.opacity = '1'
      messageEl.style.transform = 'translateY(0)'
    })
    
    // 自动移除
    setTimeout(() => {
      messageEl.style.opacity = '0'
      messageEl.style.transform = 'translateY(-20px)'
      
      setTimeout(() => {
        if (messageEl.parentNode) {
          messageEl.parentNode.removeChild(messageEl)
        }
        
        // 如果容器为空，移除容器
        if (container.children.length === 0) {
          document.body.removeChild(container)
          this.container = null
        }
      }, 300)
    }, duration)
    
    return messageEl
  }
  
  success(text: string, options?: Omit<MessageOptions, 'type'>) {
    return this.createMessage(text, { ...options, type: 'success' })
  }
  
  error(text: string, options?: Omit<MessageOptions, 'type'>) {
    return this.createMessage(text, { ...options, type: 'error' })
  }
  
  warning(text: string, options?: Omit<MessageOptions, 'type'>) {
    return this.createMessage(text, { ...options, type: 'warning' })
  }
  
  info(text: string, options?: Omit<MessageOptions, 'type'>) {
    return this.createMessage(text, { ...options, type: 'info' })
  }
}

// 导出单例实例
export const message = new MessageManager()

// 为了兼容性，也可以作为默认导出
export default message
