<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <!-- 主要内容 -->
    <div class="login-content">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-logo">
          <div class="logo-container">
            <div class="logo-circle">
              <span class="logo-text">🛡️</span>
            </div>
            <div class="logo-ripple"></div>
          </div>
          <h1 class="brand-title">
            <span class="title-char" style="animation-delay: 0.1s">膳</span>
            <span class="title-char" style="animation-delay: 0.2s">食</span>
            <span class="title-char" style="animation-delay: 0.3s">营</span>
            <span class="title-char" style="animation-delay: 0.4s">养</span>
            <span class="title-char" style="animation-delay: 0.5s">分</span>
            <span class="title-char" style="animation-delay: 0.6s">析</span>
            <span class="title-char" style="animation-delay: 0.7s">平</span>
            <span class="title-char" style="animation-delay: 0.8s">台</span>
          </h1>
          <p class="brand-subtitle">管理后台 - 系统管理中心</p>
        </div>

        <div class="feature-highlights">
          <div class="feature-item">
            <div class="feature-icon">👥</div>
            <div class="feature-content">
              <h3>用户管理</h3>
              <p>全面的用户账户管理</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📊</div>
            <div class="feature-content">
              <h3>数据统计</h3>
              <p>详细的数据分析报告</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">⚙️</div>
            <div class="feature-content">
              <h3>系统配置</h3>
              <p>灵活的系统参数设置</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2 class="form-title">管理员登录</h2>
            <div class="title-underline"></div>
            <p class="form-subtitle">请使用管理员账户登录系统</p>
          </div>

          <!-- 登录方式切换 -->
          <div class="login-mode-switch">
            <button
              type="button"
              class="mode-button"
              @click="switchLoginMode"
              v-if="loginMode === 'code'"
            >
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1L15.09 8.26L22 9L15.09 9.74L12 17L8.91 9.74L2 9L8.91 8.26L12 1Z"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              使用密码登录
            </button>
            <button
              type="button"
              class="mode-button"
              @click="switchLoginMode"
              v-if="loginMode === 'password'"
            >
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              使用验证码登录
            </button>
          </div>

          <!-- 全局错误提示 -->
          <div v-if="globalError" class="global-error-message">
            <div class="error-content">
              <svg class="error-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>{{ globalError }}</span>
            </div>
          </div>

          <form @submit.prevent="handleLogin" class="login-form">
            <!-- 密码登录表单 -->
            <template v-if="loginMode === 'password'">
              <div class="form-group">
                <label for="username" class="form-label">管理员用户名</label>
                <div class="input-wrapper">
                  <input
                    id="username"
                    v-model="loginForm.username"
                    type="text"
                    class="form-input"
                    :class="{ 'error': errors.username }"
                    placeholder="请输入管理员用户名"
                    @blur="validateUsername"
                  />
                  <span class="input-icon">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </span>
                </div>
                <span v-if="errors.username" class="error-message">{{ errors.username }}</span>
              </div>

              <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <div class="input-wrapper">
                  <input
                    id="password"
                    v-model="loginForm.password"
                    :type="showPassword ? 'text' : 'password'"
                    class="form-input"
                    :class="{ 'error': errors.password }"
                    placeholder="请输入密码"
                    @blur="validatePassword"
                  />
                  <span class="input-icon">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                      <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2"/>
                      <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </span>
                  <button type="button" class="password-toggle" @click="togglePassword">
                    <svg v-if="showPassword" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M1 12S5 4 12 4s11 8 11 8-4 8-11 8S1 12 1 12z" stroke="currentColor" stroke-width="2"/>
                      <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </button>
                </div>
                <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
              </div>
            </template>

            <!-- 验证码登录表单 -->
            <template v-else>
              <div class="form-group">
                <label for="email" class="form-label">管理员邮箱</label>
                <div class="input-wrapper">
                  <input
                    id="email"
                    v-model="emailCodeForm.email"
                    type="email"
                    class="form-input"
                    :class="{ 'error': errors.email }"
                    placeholder="请输入管理员邮箱"
                    @blur="validateEmail"
                  />
                  <span class="input-icon">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </span>
                </div>
                <span v-if="errors.email" class="error-message">{{ errors.email }}</span>
              </div>

              <div class="form-group">
                <label for="verificationCode" class="form-label">邮箱验证码</label>
                <div class="input-wrapper verification-wrapper">
                  <input
                    id="verificationCode"
                    v-model="emailCodeForm.verificationCode"
                    type="text"
                    class="form-input verification-input"
                    :class="{ 'error': errors.verificationCode }"
                    placeholder="请输入6位验证码"
                    maxlength="6"
                    @blur="validateVerificationCode"
                  />
                  <button
                    type="button"
                    class="send-code-button"
                    :class="{ 'loading': isCodeLoading, 'disabled': countdown > 0 }"
                    :disabled="isCodeLoading || countdown > 0 || !emailCodeForm.email"
                    @click="sendVerificationCode"
                  >
                    <span v-if="isCodeLoading" class="loading-spinner"></span>
                    <span v-else-if="countdown > 0">{{ countdown }}s</span>
                    <span v-else>发送验证码</span>
                  </button>
                </div>
                <span v-if="errors.verificationCode" class="error-message">{{ errors.verificationCode }}</span>
              </div>
            </template>

            <button type="submit" class="login-button" :disabled="isLoading">
              <span v-if="isLoading" class="loading-spinner"></span>
              <span>{{ isLoading ? '登录中...' : '登录管理后台' }}</span>
            </button>
          </form>

          <div class="form-footer">
            <div class="footer-links">
              <router-link to="/" class="back-link">← 返回首页</router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { adminLogin, adminLoginWithCode, sendAdminLoginCode, type AdminLoginRequest, type AdminEmailCodeLoginRequest, type ApiResponse, type Admin } from '../utils/adminApi'
import { AuthManager } from '../utils/auth'

const router = useRouter()

// 登录方式切换
const loginMode = ref<'password' | 'code'>('password')
const isCodeLoading = ref(false)
const countdown = ref(0)
const countdownTimer = ref<number | null>(null)

const loginForm = reactive({
  username: '',
  password: ''
})

const emailCodeForm = reactive({
  email: '',
  verificationCode: ''
})

const errors = reactive({
  username: '',
  password: '',
  email: '',
  verificationCode: ''
})

const isLoading = ref(false)
const showPassword = ref(false)
const globalError = ref('')

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const validateUsername = () => {
  if (!loginForm.username.trim()) {
    errors.username = '请输入管理员用户名'
  } else {
    errors.username = ''
  }
}

const validatePassword = () => {
  if (!loginForm.password.trim()) {
    errors.password = '请输入密码'
  } else {
    errors.password = ''
  }
}

const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailCodeForm.email.trim()) {
    errors.email = '请输入邮箱地址'
  } else if (!emailRegex.test(emailCodeForm.email)) {
    errors.email = '请输入有效的邮箱地址'
  } else {
    errors.email = ''
  }
}

const validateVerificationCode = () => {
  if (!emailCodeForm.verificationCode.trim()) {
    errors.verificationCode = '请输入验证码'
  } else if (emailCodeForm.verificationCode.length !== 6) {
    errors.verificationCode = '验证码必须是6位数字'
  } else {
    errors.verificationCode = ''
  }
}

// 切换登录方式
const switchLoginMode = () => {
  loginMode.value = loginMode.value === 'password' ? 'code' : 'password'
  globalError.value = ''
  // 清除表单错误
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })
}

// 发送验证码
const sendVerificationCode = async () => {
  validateEmail()
  if (errors.email) return

  isCodeLoading.value = true
  try {
    const response = await sendAdminLoginCode(emailCodeForm.email)
    if (response.success) {
      // 开始倒计时
      countdown.value = 60
      countdownTimer.value = window.setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          if (countdownTimer.value) {
            clearInterval(countdownTimer.value)
            countdownTimer.value = null
          }
        }
      }, 1000)
    } else {
      globalError.value = response.message
    }
  } catch (error: any) {
    globalError.value = error.message || '发送验证码失败，请重试'
  } finally {
    isCodeLoading.value = false
  }
}

const validateForm = () => {
  globalError.value = ''

  if (loginMode.value === 'password') {
    validateUsername()
    validatePassword()
    return !errors.username && !errors.password
  } else {
    validateEmail()
    validateVerificationCode()
    return !errors.email && !errors.verificationCode
  }
}

const handleLogin = async () => {
  if (!validateForm()) return

  isLoading.value = true

  try {
    let response: ApiResponse<Admin>

    if (loginMode.value === 'password') {
      // 密码登录
      response = await adminLogin({
        username: loginForm.username,
        password: loginForm.password
      })
    } else {
      // 验证码登录
      response = await adminLoginWithCode({
        email: emailCodeForm.email,
        verificationCode: emailCodeForm.verificationCode
      })
    }

    if (response.success && response.data) {
      // 使用AuthManager正确设置管理员状态
      const { token, ...adminData } = response.data
      if (token) {
        AuthManager.login(token, { ...adminData, userType: 'ADMIN' })
      }

      // 跳转到管理员控制台
      router.push('/admin/dashboard')
    } else {
      globalError.value = response.message
    }
  } catch (error: any) {
    globalError.value = error.message || '登录失败，请检查网络连接后重试'
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
/* CSS变量定义 - 优化后的和谐配色方案 */
:root {
  /* 主色调 - 健康绿色系 */
  --primary-green: #16a085;
  --primary-green-light: #48c9b0;
  --primary-green-dark: #138d75;
  --primary-green-ultra-light: #a3e4d7;

  /* 辅助色 - 和谐的蓝绿色系 */
  --secondary-teal: #17a2b8;
  --secondary-teal-light: #5dade2;
  --secondary-teal-dark: #148a99;

  /* 点缀色 - 温暖的橙色系（绿色的互补色） */
  --accent-orange: #e67e22;
  --accent-orange-light: #f39c12;
  --accent-orange-soft: #fdeaa7;

  /* 中性色系 - 进一步优化对比度 */
  --text-primary: #1a252f;
  --text-secondary: #2c3e50;
  --text-light: #34495e;
  --text-muted: #5d6d7e;
  --text-placeholder: #6c757d;

  /* 背景色系 */
  --background-white: #ffffff;
  --background-gray: #f8f9fa;
  --background-light: #ecf0f1;

  /* 边框色系 - 增强可见性 */
  --border-light: #bdc3c7;
  --border-medium: #95a5a6;
  --border-focus: var(--primary-green);

  /* 状态色系 */
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --error-color: #ff4444;
  --error-color-dark: #e74c3c;
  --info-color: #3498db;

  /* 阴影 */
  --shadow-sm: 0 1px 3px 0 rgba(44, 62, 80, 0.08);
  --shadow-md: 0 4px 12px 0 rgba(44, 62, 80, 0.12);
  --shadow-lg: 0 8px 25px 0 rgba(44, 62, 80, 0.15);
  --shadow-xl: 0 12px 35px 0 rgba(44, 62, 80, 0.18);

  /* 圆角 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* 主容器 */
.login-container {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #f8f9fa 0%, #ecf0f1 30%, #e8f6f3 70%, #d5f4e6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.08), rgba(72, 201, 176, 0.04));
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
  animation-delay: 0s;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
  animation-delay: 2s;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  left: 10%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 主要内容 */
.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1200px;
  background: var(--background-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 600px;
}

/* 品牌区域 */
.brand-section {
  background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-teal) 50%, var(--primary-green-light) 100%);
  color: white;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.brand-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.2;
}

.brand-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.15) 50%, rgba(0, 0, 0, 0.3) 100%);
  pointer-events: none;
}

.brand-logo {
  position: relative;
  z-index: 1;
  margin-bottom: 2rem;
}

.logo-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.logo-circle {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  animation: logoFloat 3s ease-in-out infinite;
}

.logo-ripple {
  position: absolute;
  width: 80px;
  height: 80px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: ripple 2s infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.02em;
}

.title-char {
  display: inline-block;
  animation: titleSlideIn 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes titleSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.brand-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 2rem 0;
  font-weight: 400;
  line-height: 1.5;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.feature-highlights {
  position: relative;
  z-index: 1;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.3s ease;
  animation: featureSlideIn 0.6s ease-out forwards;
  opacity: 0;
  transform: translateX(-30px);
}

.feature-item:nth-child(1) { animation-delay: 0.2s; }
.feature-item:nth-child(2) { animation-delay: 0.4s; }
.feature-item:nth-child(3) { animation-delay: 0.6s; }

@keyframes featureSlideIn {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.feature-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: white;
}

.feature-content p {
  margin: 0;
  font-size: 0.85rem;
  opacity: 0.8;
  line-height: 1.4;
}

/* 表单区域 */
.form-section {
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: var(--background-white);
}

.form-container {
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.title-underline {
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-green), var(--secondary-teal));
  margin: 0 auto 1rem auto;
  border-radius: 2px;
}

.form-subtitle {
  color: var(--text-muted);
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

/* 错误提示 */
.global-error-message {
  background: linear-gradient(135deg, #ffebee, #ffcdd2);
  border: 1px solid var(--error-color);
  border-radius: var(--radius-md);
  padding: 1rem;
  margin-bottom: 1.5rem;
  animation: errorSlideIn 0.3s ease-out;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--error-color-dark);
  font-weight: 500;
}

.error-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* 登录方式切换 */
.login-mode-switch {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.mode-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #48c9b0, #16a085);
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(22, 160, 133, 0.2);
}

.mode-button:hover {
  background: linear-gradient(135deg, #16a085, #138d75);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(22, 160, 133, 0.3);
}

.mode-button svg {
  width: 18px;
  height: 18px;
}

/* 表单样式 */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: 0.875rem 1rem 0.875rem 3rem;
  border: 2px solid #e8f4f8;
  border-radius: 8px;
  font-size: 1rem;
  color: #1a252f;
  background: #ffffff;
  transition: all 0.3s ease;
  outline: none;
}

.form-input::placeholder {
  color: #6c757d;
}

.form-input:focus {
  border-color: #16a085;
  box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);
  transform: translateY(-1px);
}

.form-input.error {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  transition: color 0.3s ease;
  z-index: 1;
}

.input-icon svg {
  width: 18px;
  height: 18px;
}

.form-input:focus + .input-icon {
  color: #16a085;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
  z-index: 2;
}

.password-toggle:hover {
  color: var(--primary-green);
  background: rgba(22, 160, 133, 0.1);
}

.password-toggle svg {
  width: 18px;
  height: 18px;
}

/* 隐藏浏览器默认的密码显示切换按钮 */
input[type="password"]::-ms-reveal,
input[type="password"]::-webkit-credentials-auto-fill-button {
  display: none;
}

/* 验证码输入框样式 */
.verification-wrapper {
  display: flex;
  gap: 0.75rem;
  align-items: stretch;
}

.verification-input {
  flex: 1;
}

.send-code-button {
  padding: 0 1.5rem;
  background: linear-gradient(135deg, #48c9b0, #16a085);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.send-code-button:hover:not(.disabled) {
  background: linear-gradient(135deg, #16a085, #138d75);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.3);
}

.send-code-button.disabled {
  background: #ecf0f1;
  color: #5d6d7e;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.send-code-button.loading {
  background: #48c9b0;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-message {
  color: var(--error-color);
  font-size: 0.8rem;
  margin-top: 0.25rem;
  margin-left: 0.25rem;
  animation: errorSlideIn 0.3s ease-out;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #138d75, #16a085, #17a2b8);
  color: #ffffff;
  border: none;
  border-radius: var(--radius-md);
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(19, 141, 117, 0.4);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #117a65, #138d75, #148a99);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(19, 141, 117, 0.6);
  color: #ffffff;
}

.login-button:active:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(19, 141, 117, 0.5);
}

.login-button:disabled {
  background: #95a5a6;
  color: #ffffff;
  opacity: 0.8;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  text-shadow: none;
}

.loading-spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 表单底部 */
.form-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-light);
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}



.back-link {
  color: var(--primary-green);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.back-link:hover {
  color: var(--primary-green-dark);
  transform: translateX(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    grid-template-columns: 1fr;
    max-width: 500px;
    min-height: auto;
  }

  .brand-section {
    padding: 2rem;
    text-align: center;
  }

  .brand-title {
    font-size: 2rem;
  }

  .feature-highlights {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-section {
    padding: 2rem;
  }

  .form-title {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 0.5rem;
  }

  .brand-section,
  .form-section {
    padding: 1.5rem;
  }

  .brand-title {
    font-size: 1.75rem;
  }

  .form-title {
    font-size: 1.5rem;
  }

  .feature-item {
    padding: 0.75rem;
  }
}
</style>
