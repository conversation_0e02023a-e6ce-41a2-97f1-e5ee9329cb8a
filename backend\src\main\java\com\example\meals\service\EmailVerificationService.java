package com.example.meals.service;

import com.example.meals.common.Result;

/**
 * 邮箱验证码服务接口
 */
public interface EmailVerificationService {
    
    /**
     * 发送验证码
     * 
     * @param email 邮箱地址
     * @param type 验证类型（REGISTER-注册, RESET_PASSWORD-重置密码）
     * @return 发送结果
     */
    Result<Void> sendVerificationCode(String email, String type);
    
    /**
     * 验证验证码
     * 
     * @param email 邮箱地址
     * @param code 验证码
     * @param type 验证类型
     * @return 验证结果
     */
    Result<Void> verifyCode(String email, String code, String type);
    
    /**
     * 检查验证码是否有效（不消费验证码）
     * 
     * @param email 邮箱地址
     * @param code 验证码
     * @param type 验证类型
     * @return 检查结果
     */
    Result<Boolean> checkCode(String email, String code, String type);
    
    /**
     * 使验证码失效
     * 
     * @param email 邮箱地址
     * @param type 验证类型
     * @return 操作结果
     */
    Result<Void> invalidateCodes(String email, String type);
    
    /**
     * 清理过期的验证码
     * 
     * @return 清理结果
     */
    Result<Integer> cleanExpiredCodes();
    
    /**
     * 检查是否可以发送验证码（防止频繁发送）
     * 
     * @param email 邮箱地址
     * @param type 验证类型
     * @return 检查结果，包含剩余等待时间（秒）
     */
    Result<Integer> checkSendLimit(String email, String type);
}
