package com.example.meals.dto.request;

/**
 * 更新用户资料请求DTO
 */
public class UpdateProfileRequest {
    
    private String realName; // 真实姓名
    private String phone; // 手机号码
    private Integer gender; // 性别：0-未知，1-男，2-女
    private String birthday; // 生日 (YYYY-MM-DD格式)
    private Integer age; // 年龄
    private Double height; // 身高(cm)
    private Double weight; // 体重(kg)
    private String allergies; // 过敏信息，JSON格式存储
    private String dietaryPreferences; // 饮食偏好，JSON格式存储
    private String healthGoals; // 健康目标，JSON格式存储
    private String bio; // 个人简介
    
    // 构造函数
    public UpdateProfileRequest() {}
    
    // Getter 和 Setter 方法
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public Integer getGender() {
        return gender;
    }
    
    public void setGender(Integer gender) {
        this.gender = gender;
    }
    
    public String getBirthday() {
        return birthday;
    }
    
    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }
    
    public Integer getAge() {
        return age;
    }
    
    public void setAge(Integer age) {
        this.age = age;
    }
    
    public Double getHeight() {
        return height;
    }
    
    public void setHeight(Double height) {
        this.height = height;
    }
    
    public Double getWeight() {
        return weight;
    }
    
    public void setWeight(Double weight) {
        this.weight = weight;
    }
    
    public String getAllergies() {
        return allergies;
    }
    
    public void setAllergies(String allergies) {
        this.allergies = allergies;
    }
    
    public String getDietaryPreferences() {
        return dietaryPreferences;
    }
    
    public void setDietaryPreferences(String dietaryPreferences) {
        this.dietaryPreferences = dietaryPreferences;
    }
    
    public String getHealthGoals() {
        return healthGoals;
    }
    
    public void setHealthGoals(String healthGoals) {
        this.healthGoals = healthGoals;
    }
    
    public String getBio() {
        return bio;
    }
    
    public void setBio(String bio) {
        this.bio = bio;
    }
    
    @Override
    public String toString() {
        return "UpdateProfileRequest{" +
                "realName='" + realName + '\'' +
                ", phone='" + phone + '\'' +
                ", gender=" + gender +
                ", birthday='" + birthday + '\'' +
                ", age=" + age +
                ", height=" + height +
                ", weight=" + weight +
                ", bio='" + bio + '\'' +
                '}';
    }
}
