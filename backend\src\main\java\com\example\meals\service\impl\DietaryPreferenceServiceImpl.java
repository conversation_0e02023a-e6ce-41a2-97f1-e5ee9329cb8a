package com.example.meals.service.impl;

import com.example.meals.common.Result;
import com.example.meals.dto.DietaryPreferenceResponse;
import com.example.meals.dto.request.DietaryPreferenceRequest;
import com.example.meals.entity.DietaryPreference;
import com.example.meals.mapper.DietaryPreferenceMapper;
import com.example.meals.service.DietaryPreferenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 饮食偏好服务实现类
 */
@Service
public class DietaryPreferenceServiceImpl implements DietaryPreferenceService {
    
    @Autowired
    private DietaryPreferenceMapper dietaryPreferenceMapper;
    
    @Override
    public Result<List<DietaryPreferenceResponse>> getAllDietaryPreferences() {
        try {
            List<DietaryPreference> preferences = dietaryPreferenceMapper.selectAll();
            List<DietaryPreferenceResponse> responses = preferences.stream()
                    .map(DietaryPreferenceResponse::new)
                    .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("获取饮食偏好列表失败");
        }
    }
    
    @Override
    public Result<List<DietaryPreferenceResponse>> getEnabledDietaryPreferences() {
        try {
            List<DietaryPreference> preferences = dietaryPreferenceMapper.selectEnabled();
            List<DietaryPreferenceResponse> responses = preferences.stream()
                    .map(DietaryPreferenceResponse::new)
                    .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("获取启用的饮食偏好列表失败");
        }
    }
    
    @Override
    public Result<DietaryPreferenceResponse> getDietaryPreferenceById(Long id) {
        try {
            DietaryPreference preference = dietaryPreferenceMapper.selectById(id);
            if (preference == null) {
                return Result.notFound("饮食偏好不存在");
            }
            return Result.success(new DietaryPreferenceResponse(preference));
        } catch (Exception e) {
            return Result.error("获取饮食偏好详情失败");
        }
    }
    
    @Override
    public Result<DietaryPreferenceResponse> createDietaryPreference(DietaryPreferenceRequest request) {
        try {
            // 验证必填字段
            if (request.getCode() == null || request.getCode().trim().isEmpty()) {
                return Result.badRequest("偏好代码不能为空");
            }
            if (request.getName() == null || request.getName().trim().isEmpty()) {
                return Result.badRequest("偏好名称不能为空");
            }
            
            // 检查代码是否已存在
            if (dietaryPreferenceMapper.countByCode(request.getCode()) > 0) {
                return Result.badRequest("偏好代码已存在");
            }
            
            // 创建实体
            DietaryPreference preference = new DietaryPreference();
            preference.setCode(request.getCode().trim());
            preference.setName(request.getName().trim());
            preference.setDescription(request.getDescription());
            preference.setSortOrder(request.getSortOrder() != null ? request.getSortOrder() : 
                                   dietaryPreferenceMapper.getMaxSortOrder() + 1);
            preference.setStatus(request.getStatus() != null ? request.getStatus() : 1);
            
            // 插入数据库
            int result = dietaryPreferenceMapper.insert(preference);
            if (result > 0) {
                return Result.success(new DietaryPreferenceResponse(preference));
            } else {
                return Result.error("创建饮食偏好失败");
            }
        } catch (Exception e) {
            return Result.error("创建饮食偏好失败");
        }
    }
    
    @Override
    public Result<DietaryPreferenceResponse> updateDietaryPreference(Long id, DietaryPreferenceRequest request) {
        try {
            // 检查记录是否存在
            DietaryPreference existing = dietaryPreferenceMapper.selectById(id);
            if (existing == null) {
                return Result.notFound("饮食偏好不存在");
            }
            
            // 验证必填字段
            if (request.getCode() == null || request.getCode().trim().isEmpty()) {
                return Result.badRequest("偏好代码不能为空");
            }
            if (request.getName() == null || request.getName().trim().isEmpty()) {
                return Result.badRequest("偏好名称不能为空");
            }
            
            // 检查代码是否已被其他记录使用
            if (dietaryPreferenceMapper.countByCodeExcludeId(request.getCode(), id) > 0) {
                return Result.badRequest("偏好代码已存在");
            }
            
            // 更新实体
            existing.setCode(request.getCode().trim());
            existing.setName(request.getName().trim());
            existing.setDescription(request.getDescription());
            existing.setSortOrder(request.getSortOrder() != null ? request.getSortOrder() : existing.getSortOrder());
            existing.setStatus(request.getStatus() != null ? request.getStatus() : existing.getStatus());
            
            // 更新数据库
            int result = dietaryPreferenceMapper.update(existing);
            if (result > 0) {
                return Result.success(new DietaryPreferenceResponse(existing));
            } else {
                return Result.error("更新饮食偏好失败");
            }
        } catch (Exception e) {
            return Result.error("更新饮食偏好失败");
        }
    }
    
    @Override
    public Result<Void> deleteDietaryPreference(Long id) {
        try {
            // 检查记录是否存在
            DietaryPreference existing = dietaryPreferenceMapper.selectById(id);
            if (existing == null) {
                return Result.notFound("饮食偏好不存在");
            }
            
            // 删除记录
            int result = dietaryPreferenceMapper.deleteById(id);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("删除饮食偏好失败");
            }
        } catch (Exception e) {
            return Result.error("删除饮食偏好失败");
        }
    }
}
