<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.EmailVerificationMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.meals.entity.EmailVerification">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="verification_code" property="verificationCode" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="use_time" property="useTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, email, verification_code, type, status, create_time, expire_time, use_time
    </sql>

    <!-- 插入验证码记录 -->
    <insert id="insert" parameterType="com.example.meals.entity.EmailVerification" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO email_verification (email, verification_code, type, status, create_time, expire_time)
        VALUES (#{email}, #{verificationCode}, #{type}, #{status}, #{createTime}, #{expireTime})
    </insert>

    <!-- 根据邮箱和类型查找最新的有效验证码 -->
    <select id="findLatestValidCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM email_verification
        WHERE email = #{email} AND type = #{type} AND status = 0 AND expire_time > NOW()
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据邮箱、验证码和类型查找验证码记录 -->
    <select id="findByEmailAndCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM email_verification
        WHERE email = #{email} AND verification_code = #{code} AND type = #{type} AND status = 0
    </select>

    <!-- 更新验证码状态 -->
    <update id="updateStatus">
        UPDATE email_verification 
        SET status = #{status}, use_time = #{useTime} 
        WHERE id = #{id}
    </update>

    <!-- 使验证码失效（将同一邮箱同一类型的所有未使用验证码标记为已过期） -->
    <update id="invalidateCodesByEmailAndType">
        UPDATE email_verification 
        SET status = 2
        WHERE email = #{email} AND type = #{type} AND status = 0
    </update>

    <!-- 清理过期的验证码记录 -->
    <delete id="deleteExpiredCodes">
        DELETE FROM email_verification 
        WHERE expire_time &lt; NOW() OR create_time &lt; #{cutoffTime}
    </delete>

    <!-- 统计指定邮箱在指定时间内发送的验证码数量 -->
    <select id="countRecentCodes" resultType="int">
        SELECT COUNT(*) 
        FROM email_verification
        WHERE email = #{email} AND type = #{type} AND create_time > #{afterTime}
    </select>

    <!-- 根据ID查找验证码记录 -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM email_verification
        WHERE id = #{id}
    </select>

    <!-- 查找指定邮箱的所有验证码记录 -->
    <select id="findByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM email_verification
        WHERE email = #{email}
        ORDER BY create_time DESC
    </select>

</mapper>
