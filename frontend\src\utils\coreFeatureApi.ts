// 核心功能相关API接口

import { authFetch } from './auth'

// 基础配置
const API_BASE_URL = 'http://localhost:8080/api/core-features'

// 包装函数，将authFetch的结果包装成ApiResponse格式
async function makeAuthenticatedRequest<T>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
  const response = await authFetch(url, options)
  return response.json()
}

// 类型定义
export interface CoreFeatureRequest {
  title: string
  description: string
  icon: string
  highlights: string[]
  sortOrder?: number
  status?: number
}

export interface CoreFeature {
  id: number
  title: string
  description: string
  icon: string
  highlights: string[]
  sortOrder: number
  status: number
  statusName: string
  createTime: string
  updateTime: string
}

export interface ApiResponse<T> {
  code: number
  message: string
  data?: T
  success: boolean
}

export interface PageResponse<T> {
  records: T[]
  total: number
  page: number
  size: number
  pages: number
}



// 获取所有启用的核心功能（用于前端展示，无需认证）
export async function getAllEnabledCoreFeatures(): Promise<ApiResponse<CoreFeature[]>> {
  const response = await fetch(`${API_BASE_URL}/enabled`)
  return response.json()
}

// 创建核心功能（管理员功能，需要认证）
export async function createCoreFeature(data: CoreFeatureRequest): Promise<ApiResponse<CoreFeature>> {
  return makeAuthenticatedRequest<CoreFeature>(`${API_BASE_URL}/admin/create`, {
    method: 'POST',
    body: JSON.stringify(data),
  })
}

// 更新核心功能（管理员功能，需要认证）
export async function updateCoreFeature(id: number, data: CoreFeatureRequest): Promise<ApiResponse<CoreFeature>> {
  return makeAuthenticatedRequest<CoreFeature>(`${API_BASE_URL}/admin/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  })
}

// 删除核心功能（管理员功能，需要认证）
export async function deleteCoreFeature(id: number): Promise<ApiResponse<void>> {
  return makeAuthenticatedRequest<void>(`${API_BASE_URL}/admin/${id}`, {
    method: 'DELETE',
  })
}

// 获取核心功能详情（管理员功能，需要认证）
export async function getCoreFeatureById(id: number): Promise<ApiResponse<CoreFeature>> {
  return makeAuthenticatedRequest<CoreFeature>(`${API_BASE_URL}/admin/${id}`)
}

// 获取核心功能列表（管理员功能）
export async function getCoreFeatureList(params: {
  page?: number
  size?: number
  title?: string
  status?: number
}): Promise<ApiResponse<PageResponse<CoreFeature>>> {
  const searchParams = new URLSearchParams()
  
  if (params.page) searchParams.append('page', params.page.toString())
  if (params.size) searchParams.append('size', params.size.toString())
  if (params.title) searchParams.append('title', params.title)
  if (params.status !== undefined) searchParams.append('status', params.status.toString())

  return makeAuthenticatedRequest<PageResponse<CoreFeature>>(`${API_BASE_URL}/admin/list?${searchParams.toString()}`)
}

// 更新核心功能状态（管理员功能，需要认证）
export async function updateCoreFeatureStatus(id: number, status: number): Promise<ApiResponse<void>> {
  return makeAuthenticatedRequest<void>(`${API_BASE_URL}/admin/${id}/status?status=${status}`, {
    method: 'PUT',
  })
}

// 更新核心功能排序（管理员功能，需要认证）
export async function updateCoreFeatureSortOrder(id: number, sortOrder: number): Promise<ApiResponse<void>> {
  return makeAuthenticatedRequest<void>(`${API_BASE_URL}/admin/${id}/sort-order?sortOrder=${sortOrder}`, {
    method: 'PUT',
  })
}

// 批量更新排序（管理员功能，需要认证）
export async function batchUpdateSortOrder(ids: number[]): Promise<ApiResponse<void>> {
  return makeAuthenticatedRequest<void>(`${API_BASE_URL}/admin/batch-sort`, {
    method: 'PUT',
    body: JSON.stringify(ids),
  })
}
