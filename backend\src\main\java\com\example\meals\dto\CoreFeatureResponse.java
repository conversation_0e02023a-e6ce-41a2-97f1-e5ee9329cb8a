package com.example.meals.dto;

import com.example.meals.entity.CoreFeature;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 核心功能响应DTO
 */
public class CoreFeatureResponse {
    
    private Long id;
    private String title;
    private String description;
    private String icon;
    private List<String> highlights;
    private Integer sortOrder;
    private Integer status;
    private String statusName;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    // 构造函数
    public CoreFeatureResponse() {}
    
    public CoreFeatureResponse(CoreFeature coreFeature) {
        if (coreFeature != null) {
            this.id = coreFeature.getId();
            this.title = coreFeature.getTitle();
            this.description = coreFeature.getDescription();
            this.icon = coreFeature.getIcon();
            this.highlights = coreFeature.getHighlights();
            this.sortOrder = coreFeature.getSortOrder();
            this.status = coreFeature.getStatus();
            this.statusName = coreFeature.getStatusName();
            this.createTime = coreFeature.getCreateTime();
            this.updateTime = coreFeature.getUpdateTime();
        }
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getIcon() {
        return icon;
    }
    
    public void setIcon(String icon) {
        this.icon = icon;
    }
    
    public List<String> getHighlights() {
        return highlights;
    }
    
    public void setHighlights(List<String> highlights) {
        this.highlights = highlights;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getStatusName() {
        return statusName;
    }
    
    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    @Override
    public String toString() {
        return "CoreFeatureResponse{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", icon='" + icon + '\'' +
                ", highlights=" + highlights +
                ", sortOrder=" + sortOrder +
                ", status=" + status +
                ", statusName='" + statusName + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
