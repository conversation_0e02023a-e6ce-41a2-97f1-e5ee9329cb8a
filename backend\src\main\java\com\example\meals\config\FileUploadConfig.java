package com.example.meals.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 文件上传配置
 */
@Configuration
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {
    
    // 上传文件存储路径
    private String path = "uploads/";
    
    // 头像文件存储路径
    private String avatarPath = "uploads/avatars/";
    
    // 允许的文件类型
    private String[] allowedTypes = {"jpg", "jpeg", "png", "gif"};
    
    // 最大文件大小（字节）
    private long maxSize = 10 * 1024 * 1024; // 10MB - 与application.yml保持一致
    
    // Getter 和 Setter 方法
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    public String getAvatarPath() {
        return avatarPath;
    }
    
    public void setAvatarPath(String avatarPath) {
        this.avatarPath = avatarPath;
    }
    
    public String[] getAllowedTypes() {
        return allowedTypes;
    }
    
    public void setAllowedTypes(String[] allowedTypes) {
        this.allowedTypes = allowedTypes;
    }
    
    public long getMaxSize() {
        return maxSize;
    }
    
    public void setMaxSize(long maxSize) {
        this.maxSize = maxSize;
    }
    
}
