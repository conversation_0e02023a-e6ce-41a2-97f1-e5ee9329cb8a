package com.example.meals.controller;

import com.example.meals.common.Result;
import com.example.meals.dto.HealthGoalResponse;
import com.example.meals.dto.request.HealthGoalRequest;
import com.example.meals.service.HealthGoalService;
import com.example.meals.utils.AuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 健康目标控制器
 */
@RestController
@RequestMapping("/api/health-goals")
public class HealthGoalController {
    
    @Autowired
    private HealthGoalService healthGoalService;
    
    /**
     * 获取所有健康目标（管理员功能）
     */
    @GetMapping("/admin/all")
    public Result<List<HealthGoalResponse>> getAllHealthGoals(HttpServletRequest request) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return healthGoalService.getAllHealthGoals();
    }
    
    /**
     * 获取启用的健康目标（公开接口，用于用户选择）
     */
    @GetMapping("/enabled")
    public Result<List<HealthGoalResponse>> getEnabledHealthGoals() {
        return healthGoalService.getEnabledHealthGoals();
    }
    
    /**
     * 根据ID获取健康目标详情（管理员功能）
     */
    @GetMapping("/admin/{id}")
    public Result<HealthGoalResponse> getHealthGoalById(@PathVariable Long id, HttpServletRequest request) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return healthGoalService.getHealthGoalById(id);
    }
    
    /**
     * 创建健康目标（管理员功能）
     */
    @PostMapping("/admin/create")
    public Result<HealthGoalResponse> createHealthGoal(
            @RequestBody HealthGoalRequest request, 
            HttpServletRequest httpRequest) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(httpRequest);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return healthGoalService.createHealthGoal(request);
    }
    
    /**
     * 更新健康目标（管理员功能）
     */
    @PutMapping("/admin/{id}")
    public Result<HealthGoalResponse> updateHealthGoal(
            @PathVariable Long id,
            @RequestBody HealthGoalRequest request, 
            HttpServletRequest httpRequest) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(httpRequest);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return healthGoalService.updateHealthGoal(id, request);
    }
    
    /**
     * 删除健康目标（管理员功能）
     */
    @DeleteMapping("/admin/{id}")
    public Result<Void> deleteHealthGoal(@PathVariable Long id, HttpServletRequest request) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return healthGoalService.deleteHealthGoal(id);
    }
}
