package com.example.meals.service;

import com.example.meals.common.Result;

/**
 * 邮件发送服务接口
 */
public interface EmailService {
    
    /**
     * 发送验证码邮件
     * 
     * @param toEmail 收件人邮箱
     * @param verificationCode 验证码
     * @param type 验证类型（REGISTER-注册, RESET_PASSWORD-重置密码）
     * @return 发送结果
     */
    Result<Void> sendVerificationCode(String toEmail, String verificationCode, String type);
    
    /**
     * 发送普通邮件
     * 
     * @param toEmail 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 发送结果
     */
    Result<Void> sendEmail(String toEmail, String subject, String content);
    
    /**
     * 测试邮件连接
     * 
     * @return 测试结果
     */
    Result<Void> testConnection();
}
