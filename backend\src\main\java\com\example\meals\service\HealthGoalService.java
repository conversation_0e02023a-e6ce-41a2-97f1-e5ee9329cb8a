package com.example.meals.service;

import com.example.meals.common.Result;
import com.example.meals.dto.HealthGoalResponse;
import com.example.meals.dto.request.HealthGoalRequest;

import java.util.List;

/**
 * 健康目标服务接口
 */
public interface HealthGoalService {
    
    /**
     * 获取所有健康目标
     */
    Result<List<HealthGoalResponse>> getAllHealthGoals();
    
    /**
     * 获取启用的健康目标
     */
    Result<List<HealthGoalResponse>> getEnabledHealthGoals();
    
    /**
     * 根据ID获取健康目标
     */
    Result<HealthGoalResponse> getHealthGoalById(Long id);
    
    /**
     * 创建健康目标
     */
    Result<HealthGoalResponse> createHealthGoal(HealthGoalRequest request);
    
    /**
     * 更新健康目标
     */
    Result<HealthGoalResponse> updateHealthGoal(Long id, HealthGoalRequest request);
    
    /**
     * 删除健康目标
     */
    Result<Void> deleteHealthGoal(Long id);
}
