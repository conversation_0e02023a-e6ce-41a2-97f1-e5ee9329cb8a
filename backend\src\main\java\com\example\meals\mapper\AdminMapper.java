package com.example.meals.mapper;

import com.example.meals.entity.Admin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理员数据访问层
 */
@Mapper
public interface AdminMapper {
    
    /**
     * 根据ID查询管理员
     */
    Admin selectById(@Param("id") Long id);
    
    /**
     * 根据用户名查询管理员
     */
    Admin selectByUsername(@Param("username") String username);
    
    /**
     * 根据邮箱查询管理员
     */
    Admin selectByEmail(@Param("email") String email);
    
    /**
     * 查询所有管理员（分页）
     */
    List<Admin> selectAll(@Param("offset") Integer offset, @Param("limit") Integer limit);
    
    /**
     * 根据条件查询管理员
     */
    List<Admin> selectByCondition(@Param("username") String username, 
                                  @Param("realName") String realName,
                                  @Param("role") Integer role,
                                  @Param("status") Integer status,
                                  @Param("offset") Integer offset, 
                                  @Param("limit") Integer limit);
    
    /**
     * 统计管理员总数
     */
    int countAll();
    
    /**
     * 根据条件统计管理员数量
     */
    int countByCondition(@Param("username") String username, 
                         @Param("realName") String realName,
                         @Param("role") Integer role,
                         @Param("status") Integer status);
    
    /**
     * 插入管理员
     */
    int insert(Admin admin);
    
    /**
     * 更新管理员信息
     */
    int updateById(Admin admin);
    
    /**
     * 更新最后登录时间
     */
    int updateLastLoginTime(@Param("id") Long id, @Param("lastLoginTime") LocalDateTime lastLoginTime);
    
    /**
     * 根据ID删除管理员
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 检查用户名是否存在
     */
    int countByUsername(@Param("username") String username);
    
    /**
     * 检查邮箱是否存在
     */
    int countByEmail(@Param("email") String email);
}
