package com.example.meals.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;

/**
 * Spring Security 配置类
 * 禁用默认的安全配置，使用自定义的JWT拦截器
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    /**
     * 密码编码器Bean
     * 使用BCrypt算法，成本因子为12（推荐值，平衡安全性和性能）
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12);
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
            // 禁用CSRF（因为使用JWT）
            .csrf(csrf -> csrf.disable())

            // 禁用Session（使用JWT无状态认证）
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            )

            // 配置请求授权
            .authorizeHttpRequests(authz -> authz
                // 允许所有请求（由自定义拦截器处理认证）
                .anyRequest().permitAll()
            )

            // 禁用CORS（由WebConfig处理）
            .cors(cors -> cors.disable())

            // 配置安全头
            .headers(headers -> headers
                .frameOptions(frame -> frame.deny())
                .contentTypeOptions(contentType -> {})
                .httpStrictTransportSecurity(hsts -> hsts
                    .maxAgeInSeconds(31536000)
                    .includeSubDomains(true)
                )
                .referrerPolicy(referrer -> referrer
                    .policy(ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN)
                )
            )
            .build();
    }



    /**
     * 自定义UserDetailsService，防止Spring Security生成默认用户
     * 我们使用自定义的JWT认证，不需要Spring Security的默认用户认证
     */
    @Bean
    public UserDetailsService userDetailsService() {
        // 创建一个空的用户详情服务，防止自动配置生成默认用户
        return new InMemoryUserDetailsManager();
    }
}
