/**
 * API配置管理
 * 统一管理所有API相关的配置，避免硬编码
 */

// 基础API配置
export const API_CONFIG = {
  // 基础URL - 从环境变量获取，默认为开发环境
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080',
  
  // API端点
  ENDPOINTS: {
    // 用户相关
    USER: {
      LOGIN: '/api/auth/login',
      REGISTER: '/api/auth/register',
      PROFILE: '/api/users/profile',
      AVATAR_UPLOAD: '/api/files/avatar/user',
      AVATAR_DELETE: '/api/files/avatar',
    },
    
    // 管理员相关
    ADMIN: {
      LOGIN: '/api/auth/admin/login',
      PROFILE: '/api/admin/profile',
      AVATAR_UPLOAD: '/api/files/avatar/admin',
      AVATAR_DELETE: '/api/files/avatar',
    },
    
    // 文件相关
    FILES: {
      UPLOADS: '/api/files/uploads',
    }
  },
  
  // 请求配置
  REQUEST: {
    TIMEOUT: 30000, // 30秒超时
    RETRY_COUNT: 3, // 重试次数
  },
  
  // 文件上传配置
  UPLOAD: {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
    ALLOWED_EXTENSIONS: ['jpg', 'jpeg', 'png', 'gif'],
  }
}

/**
 * 获取完整的API URL
 * @param endpoint 端点路径
 * @returns 完整的URL
 */
export function getApiUrl(endpoint: string): string {
  return `${API_CONFIG.BASE_URL}${endpoint}`
}

/**
 * 获取头像URL
 * @param avatarPath 头像路径
 * @returns 完整的头像URL或null
 */
export function getAvatarUrl(avatarPath: string | null | undefined): string | null {
  if (!avatarPath) return null

  // 如果已经是完整URL，直接返回
  if (avatarPath.startsWith('http://') || avatarPath.startsWith('https://')) {
    return avatarPath
  }

  // 如果是API路径格式，直接拼接域名
  if (avatarPath.startsWith('/api/files/')) {
    return `${API_CONFIG.BASE_URL}${avatarPath}`
  }

  // 处理Windows本地路径（从后端上传的文件）
  if (avatarPath.includes('\\uploads\\avatars\\')) {
    const fileName = avatarPath.split('\\').pop()
    return `${API_CONFIG.BASE_URL}/api/files/uploads/avatars/${fileName}`
  }

  // 兼容旧格式：构建完整的头像URL
  return `${API_CONFIG.BASE_URL}/api/files/${avatarPath}`
}

/**
 * 验证文件类型
 * @param file 文件对象
 * @returns 是否为允许的类型
 */
export function validateFileType(file: File): boolean {
  return API_CONFIG.UPLOAD.ALLOWED_TYPES.includes(file.type)
}

/**
 * 验证文件大小
 * @param file 文件对象
 * @returns 是否在允许的大小范围内
 */
export function validateFileSize(file: File): boolean {
  return file.size <= API_CONFIG.UPLOAD.MAX_SIZE
}

/**
 * 获取文件大小的可读格式
 * @param bytes 字节数
 * @returns 格式化的文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 获取最大文件大小的可读格式
 * @returns 格式化的最大文件大小
 */
export function getMaxFileSizeText(): string {
  return formatFileSize(API_CONFIG.UPLOAD.MAX_SIZE)
}

/**
 * 获取允许的文件类型文本
 * @returns 允许的文件类型描述
 */
export function getAllowedTypesText(): string {
  return API_CONFIG.UPLOAD.ALLOWED_EXTENSIONS.map(ext => ext.toUpperCase()).join('、')
}
