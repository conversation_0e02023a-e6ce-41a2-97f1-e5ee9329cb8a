package com.example.meals.service;

import com.example.meals.common.Result;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件服务接口
 */
public interface FileService {
    
    /**
     * 上传头像文件
     * 
     * @param file 上传的文件
     * @param userId 用户ID
     * @param userType 用户类型（USER/ADMIN）
     * @return 上传结果，包含文件路径
     */
    Result<String> uploadAvatar(MultipartFile file, Long userId, String userType);
    
    /**
     * 删除头像文件
     * 
     * @param filePath 文件路径
     * @return 删除结果
     */
    Result<Void> deleteAvatar(String filePath);
    
    /**
     * 获取文件的完整URL
     * 
     * @param filePath 文件路径
     * @return 完整的文件URL
     */
    String getFileUrl(String filePath);
    
    /**
     * 验证文件是否为有效的图片文件
     * 
     * @param file 上传的文件
     * @return 验证结果
     */
    Result<Void> validateImageFile(MultipartFile file);
    
    /**
     * 清理过期的临时文件
     * 
     * @return 清理结果
     */
    Result<Integer> cleanTempFiles();
}
