<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.CoreFeatureMapper">

    <!-- 结果映射 -->
    <resultMap id="CoreFeatureResultMap" type="com.example.meals.entity.CoreFeature">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="description" column="description"/>
        <result property="icon" column="icon"/>
        <result property="highlightsJson" column="highlights"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, title, description, icon, highlights, sort_order, status, create_time, update_time
    </sql>

    <!-- 插入核心功能 -->
    <insert id="insert" parameterType="com.example.meals.entity.CoreFeature" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO core_feature (
            title, description, icon, highlights, sort_order, status, create_time, update_time
        ) VALUES (
            #{title}, #{description}, #{icon}, #{highlightsJson}, #{sortOrder}, #{status}, NOW(), NOW()
        )
    </insert>

    <!-- 根据ID删除核心功能 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM core_feature WHERE id = #{id}
    </delete>

    <!-- 根据ID更新核心功能 -->
    <update id="updateById" parameterType="com.example.meals.entity.CoreFeature">
        UPDATE core_feature
        <set>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="icon != null and icon != ''">
                icon = #{icon},
            </if>
            <if test="highlightsJson != null and highlightsJson != ''">
                highlights = #{highlightsJson},
            </if>
            <if test="sortOrder != null">
                sort_order = #{sortOrder},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询核心功能 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="CoreFeatureResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM core_feature
        WHERE id = #{id}
    </select>

    <!-- 查询所有启用的核心功能（按排序顺序） -->
    <select id="selectAllEnabled" resultMap="CoreFeatureResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM core_feature
        WHERE status = 1
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 分页查询核心功能列表 -->
    <select id="selectByCondition" resultMap="CoreFeatureResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM core_feature
        <where>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY sort_order ASC, id ASC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 统计核心功能数量 -->
    <select id="countByCondition" resultType="int">
        SELECT COUNT(*)
        FROM core_feature
        <where>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

    <!-- 更新排序顺序 -->
    <update id="updateSortOrder">
        UPDATE core_feature
        SET sort_order = #{sortOrder}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新状态 -->
    <update id="updateStatus">
        UPDATE core_feature
        SET status = #{status}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 获取最大排序顺序 -->
    <select id="getMaxSortOrder" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(sort_order), 0)
        FROM core_feature
    </select>

</mapper>
