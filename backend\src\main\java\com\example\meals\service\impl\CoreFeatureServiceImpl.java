package com.example.meals.service.impl;

import com.example.meals.dto.*;
import com.example.meals.entity.CoreFeature;
import com.example.meals.mapper.CoreFeatureMapper;
import com.example.meals.service.CoreFeatureService;
import com.example.meals.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 核心功能服务实现类
 */
@Service
public class CoreFeatureServiceImpl implements CoreFeatureService {
    
    @Autowired
    private CoreFeatureMapper coreFeatureMapper;
    
    @Override
    public Result<CoreFeatureResponse> createCoreFeature(CoreFeatureRequest request) {
        // 参数验证
        if (!StringUtils.hasText(request.getTitle())) {
            return Result.badRequest("功能标题不能为空");
        }
        if (!StringUtils.hasText(request.getDescription())) {
            return Result.badRequest("功能描述不能为空");
        }
        if (!StringUtils.hasText(request.getIcon())) {
            return Result.badRequest("功能图标不能为空");
        }
        if (request.getHighlights() == null || request.getHighlights().isEmpty()) {
            return Result.badRequest("功能亮点不能为空");
        }
        
        try {
            // 创建核心功能对象
            CoreFeature coreFeature = new CoreFeature();
            coreFeature.setTitle(request.getTitle());
            coreFeature.setDescription(request.getDescription());
            coreFeature.setIcon(request.getIcon());
            coreFeature.setHighlights(request.getHighlights());
            
            // 设置排序顺序，如果未指定则设为最大值+1
            if (request.getSortOrder() != null) {
                coreFeature.setSortOrder(request.getSortOrder());
            } else {
                Integer maxSortOrder = coreFeatureMapper.getMaxSortOrder();
                coreFeature.setSortOrder(maxSortOrder + 1);
            }
            
            // 设置状态，默认启用
            coreFeature.setStatus(request.getStatus() != null ? request.getStatus() : 1);
            
            // 保存核心功能
            int result = coreFeatureMapper.insert(coreFeature);
            if (result > 0) {
                return Result.success("创建成功", new CoreFeatureResponse(coreFeature));
            } else {
                return Result.error("创建失败，请重试");
            }
        } catch (Exception e) {
            return Result.error("创建失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<CoreFeatureResponse> updateCoreFeature(Long id, CoreFeatureRequest request) {
        if (id == null || id <= 0) {
            return Result.badRequest("功能ID不能为空");
        }
        
        // 查询核心功能是否存在
        CoreFeature existingFeature = coreFeatureMapper.selectById(id);
        if (existingFeature == null) {
            return Result.notFound("核心功能不存在");
        }
        
        try {
            // 更新核心功能信息
            CoreFeature coreFeature = new CoreFeature();
            coreFeature.setId(id);
            
            if (StringUtils.hasText(request.getTitle())) {
                coreFeature.setTitle(request.getTitle());
            }
            if (StringUtils.hasText(request.getDescription())) {
                coreFeature.setDescription(request.getDescription());
            }
            if (StringUtils.hasText(request.getIcon())) {
                coreFeature.setIcon(request.getIcon());
            }
            if (request.getHighlights() != null && !request.getHighlights().isEmpty()) {
                coreFeature.setHighlights(request.getHighlights());
            }
            if (request.getSortOrder() != null) {
                coreFeature.setSortOrder(request.getSortOrder());
            }
            if (request.getStatus() != null) {
                coreFeature.setStatus(request.getStatus());
            }
            
            int result = coreFeatureMapper.updateById(coreFeature);
            if (result > 0) {
                // 重新查询更新后的核心功能信息
                CoreFeature updatedFeature = coreFeatureMapper.selectById(id);
                return Result.success("更新成功", new CoreFeatureResponse(updatedFeature));
            } else {
                return Result.error("更新失败，请重试");
            }
        } catch (Exception e) {
            return Result.error("更新失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> deleteCoreFeature(Long id) {
        if (id == null || id <= 0) {
            return Result.badRequest("功能ID不能为空");
        }
        
        // 查询核心功能是否存在
        CoreFeature coreFeature = coreFeatureMapper.selectById(id);
        if (coreFeature == null) {
            return Result.notFound("核心功能不存在");
        }
        
        try {
            int result = coreFeatureMapper.deleteById(id);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("删除失败，请重试");
            }
        } catch (Exception e) {
            return Result.error("删除失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<CoreFeatureResponse> getCoreFeatureById(Long id) {
        if (id == null || id <= 0) {
            return Result.badRequest("功能ID不能为空");
        }
        
        CoreFeature coreFeature = coreFeatureMapper.selectById(id);
        if (coreFeature == null) {
            return Result.notFound("核心功能不存在");
        }
        
        return Result.success(new CoreFeatureResponse(coreFeature));
    }
    
    @Override
    public Result<List<CoreFeatureResponse>> getAllEnabledCoreFeatures() {
        try {
            List<CoreFeature> coreFeatures = coreFeatureMapper.selectAllEnabled();
            List<CoreFeatureResponse> responseList = coreFeatures.stream()
                    .map(CoreFeatureResponse::new)
                    .collect(Collectors.toList());
            
            return Result.success(responseList);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<PageResponse<CoreFeatureResponse>> getCoreFeatureList(Integer page, Integer size, 
                                                                        String title, Integer status) {
        // 参数验证和默认值设置
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1 || size > 100) {
            size = 10;
        }
        
        try {
            // 计算偏移量
            int offset = (page - 1) * size;
            
            // 查询核心功能列表
            List<CoreFeature> coreFeatures = coreFeatureMapper.selectByCondition(title, status, offset, size);
            List<CoreFeatureResponse> responseList = coreFeatures.stream()
                    .map(CoreFeatureResponse::new)
                    .collect(Collectors.toList());
            
            // 查询总数
            int total = coreFeatureMapper.countByCondition(title, status);
            
            // 构建分页响应
            PageResponse<CoreFeatureResponse> pageResponse = PageResponse.of(responseList, (long) total, page, size);
            
            return Result.success(pageResponse);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> updateCoreFeatureStatus(Long id, Integer status) {
        if (id == null || id <= 0) {
            return Result.badRequest("功能ID不能为空");
        }
        if (status == null || (status != 0 && status != 1)) {
            return Result.badRequest("状态参数错误");
        }
        
        // 查询核心功能是否存在
        CoreFeature coreFeature = coreFeatureMapper.selectById(id);
        if (coreFeature == null) {
            return Result.notFound("核心功能不存在");
        }
        
        try {
            int result = coreFeatureMapper.updateStatus(id, status);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("操作失败，请重试");
            }
        } catch (Exception e) {
            return Result.error("操作失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> updateCoreFeatureSortOrder(Long id, Integer sortOrder) {
        if (id == null || id <= 0) {
            return Result.badRequest("功能ID不能为空");
        }
        if (sortOrder == null || sortOrder < 0) {
            return Result.badRequest("排序顺序参数错误");
        }
        
        // 查询核心功能是否存在
        CoreFeature coreFeature = coreFeatureMapper.selectById(id);
        if (coreFeature == null) {
            return Result.notFound("核心功能不存在");
        }
        
        try {
            int result = coreFeatureMapper.updateSortOrder(id, sortOrder);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("排序更新失败，请重试");
            }
        } catch (Exception e) {
            return Result.error("排序更新失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> batchUpdateSortOrder(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Result.badRequest("功能ID列表不能为空");
        }
        
        try {
            // 批量更新排序顺序
            for (int i = 0; i < ids.size(); i++) {
                Long id = ids.get(i);
                Integer sortOrder = i + 1;
                coreFeatureMapper.updateSortOrder(id, sortOrder);
            }

            return Result.success();
        } catch (Exception e) {
            return Result.error("批量排序更新失败：" + e.getMessage());
        }
    }
}
