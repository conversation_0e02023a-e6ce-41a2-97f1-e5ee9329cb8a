package com.example.meals.mapper;

import com.example.meals.entity.CoreFeature;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 核心功能数据访问接口
 */
@Mapper
public interface CoreFeatureMapper {
    
    /**
     * 插入核心功能
     */
    int insert(CoreFeature coreFeature);
    
    /**
     * 根据ID删除核心功能
     */
    int deleteById(Long id);
    
    /**
     * 根据ID更新核心功能
     */
    int updateById(CoreFeature coreFeature);
    
    /**
     * 根据ID查询核心功能
     */
    CoreFeature selectById(Long id);
    
    /**
     * 查询所有启用的核心功能（按排序顺序）
     */
    List<CoreFeature> selectAllEnabled();
    
    /**
     * 分页查询核心功能列表
     */
    List<CoreFeature> selectByCondition(
            @Param("title") String title,
            @Param("status") Integer status,
            @Param("offset") int offset,
            @Param("size") int size
    );
    
    /**
     * 统计核心功能数量
     */
    int countByCondition(
            @Param("title") String title,
            @Param("status") Integer status
    );
    
    /**
     * 更新排序顺序
     */
    int updateSortOrder(@Param("id") Long id, @Param("sortOrder") Integer sortOrder);
    
    /**
     * 更新状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 获取最大排序顺序
     */
    Integer getMaxSortOrder();
}
