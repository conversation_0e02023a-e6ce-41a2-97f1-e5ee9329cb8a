package com.example.meals.controller;

import com.example.meals.common.Result;
import com.example.meals.dto.DietaryPreferenceResponse;
import com.example.meals.dto.request.DietaryPreferenceRequest;
import com.example.meals.service.DietaryPreferenceService;
import com.example.meals.utils.AuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 饮食偏好控制器
 */
@RestController
@RequestMapping("/api/dietary-preferences")
public class DietaryPreferenceController {
    
    @Autowired
    private DietaryPreferenceService dietaryPreferenceService;
    
    /**
     * 获取所有饮食偏好（管理员功能）
     */
    @GetMapping("/admin/all")
    public Result<List<DietaryPreferenceResponse>> getAllDietaryPreferences(HttpServletRequest request) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return dietaryPreferenceService.getAllDietaryPreferences();
    }
    
    /**
     * 获取启用的饮食偏好（公开接口，用于用户选择）
     */
    @GetMapping("/enabled")
    public Result<List<DietaryPreferenceResponse>> getEnabledDietaryPreferences() {
        return dietaryPreferenceService.getEnabledDietaryPreferences();
    }
    
    /**
     * 根据ID获取饮食偏好详情（管理员功能）
     */
    @GetMapping("/admin/{id}")
    public Result<DietaryPreferenceResponse> getDietaryPreferenceById(@PathVariable Long id, HttpServletRequest request) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return dietaryPreferenceService.getDietaryPreferenceById(id);
    }
    
    /**
     * 创建饮食偏好（管理员功能）
     */
    @PostMapping("/admin/create")
    public Result<DietaryPreferenceResponse> createDietaryPreference(
            @RequestBody DietaryPreferenceRequest request, 
            HttpServletRequest httpRequest) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(httpRequest);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return dietaryPreferenceService.createDietaryPreference(request);
    }
    
    /**
     * 更新饮食偏好（管理员功能）
     */
    @PutMapping("/admin/{id}")
    public Result<DietaryPreferenceResponse> updateDietaryPreference(
            @PathVariable Long id,
            @RequestBody DietaryPreferenceRequest request, 
            HttpServletRequest httpRequest) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(httpRequest);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return dietaryPreferenceService.updateDietaryPreference(id, request);
    }
    
    /**
     * 删除饮食偏好（管理员功能）
     */
    @DeleteMapping("/admin/{id}")
    public Result<Void> deleteDietaryPreference(@PathVariable Long id, HttpServletRequest request) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return dietaryPreferenceService.deleteDietaryPreference(id);
    }
}
