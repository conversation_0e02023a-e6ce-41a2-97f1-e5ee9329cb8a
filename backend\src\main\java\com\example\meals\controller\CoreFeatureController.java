package com.example.meals.controller;

import com.example.meals.dto.*;
import com.example.meals.service.CoreFeatureService;
import com.example.meals.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;

import java.util.List;

/**
 * 核心功能控制器
 */
@RestController
@RequestMapping("/api/core-features")
public class CoreFeatureController {
    
    @Autowired
    private CoreFeatureService coreFeatureService;
    
    /**
     * 获取所有启用的核心功能（用于前端展示）
     */
    @GetMapping("/enabled")
    public Result<List<CoreFeatureResponse>> getAllEnabledCoreFeatures() {
        return coreFeatureService.getAllEnabledCoreFeatures();
    }
    
    /**
     * 创建核心功能（管理员功能）
     */
    @PostMapping("/admin/create")
    public Result<CoreFeatureResponse> createCoreFeature(@RequestBody CoreFeatureRequest request, HttpServletRequest httpRequest) {
        // 验证管理员权限
        String userType = (String) httpRequest.getAttribute("userType");
        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }
        return coreFeatureService.createCoreFeature(request);
    }
    
    /**
     * 更新核心功能（管理员功能）
     */
    @PutMapping("/admin/{id}")
    public Result<CoreFeatureResponse> updateCoreFeature(@PathVariable Long id, @RequestBody CoreFeatureRequest request, HttpServletRequest httpRequest) {
        // 验证管理员权限
        String userType = (String) httpRequest.getAttribute("userType");
        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }
        return coreFeatureService.updateCoreFeature(id, request);
    }
    
    /**
     * 删除核心功能（管理员功能）
     */
    @DeleteMapping("/admin/{id}")
    public Result<Void> deleteCoreFeature(@PathVariable Long id, HttpServletRequest httpRequest) {
        // 验证管理员权限
        String userType = (String) httpRequest.getAttribute("userType");
        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }
        return coreFeatureService.deleteCoreFeature(id);
    }
    
    /**
     * 根据ID获取核心功能（管理员功能）
     */
    @GetMapping("/admin/{id}")
    public Result<CoreFeatureResponse> getCoreFeatureById(@PathVariable Long id, HttpServletRequest httpRequest) {
        // 验证管理员权限
        String userType = (String) httpRequest.getAttribute("userType");
        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }
        return coreFeatureService.getCoreFeatureById(id);
    }
    
    /**
     * 分页查询核心功能列表（管理员功能）
     */
    @GetMapping("/admin/list")
    public Result<PageResponse<CoreFeatureResponse>> getCoreFeatureList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) Integer status,
            HttpServletRequest httpRequest) {
        // 验证管理员权限
        String userType = (String) httpRequest.getAttribute("userType");
        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }
        return coreFeatureService.getCoreFeatureList(page, size, title, status);
    }
    
    /**
     * 更新核心功能状态（管理员功能）
     */
    @PutMapping("/admin/{id}/status")
    public Result<Void> updateCoreFeatureStatus(@PathVariable Long id, @RequestParam Integer status, HttpServletRequest httpRequest) {
        // 验证管理员权限
        String userType = (String) httpRequest.getAttribute("userType");
        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }
        return coreFeatureService.updateCoreFeatureStatus(id, status);
    }
    
    /**
     * 更新核心功能排序（管理员功能）
     */
    @PutMapping("/admin/{id}/sort-order")
    public Result<Void> updateCoreFeatureSortOrder(@PathVariable Long id, @RequestParam Integer sortOrder) {
        return coreFeatureService.updateCoreFeatureSortOrder(id, sortOrder);
    }
    
    /**
     * 批量更新排序（管理员功能）
     */
    @PutMapping("/admin/batch-sort")
    public Result<Void> batchUpdateSortOrder(@RequestBody List<Long> ids) {
        return coreFeatureService.batchUpdateSortOrder(ids);
    }
}
