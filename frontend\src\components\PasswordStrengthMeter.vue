<template>
  <div class="password-strength-meter">
    <div class="strength-bar">
      <div 
        class="strength-fill" 
        :class="strengthClass"
        :style="{ width: strengthPercentage + '%' }"
      ></div>
    </div>
    <div class="strength-text">
      <span :class="strengthClass">{{ strengthText }}</span>
    </div>
    <div v-if="suggestions.length > 0" class="suggestions">
      <p class="suggestions-title">密码建议：</p>
      <ul>
        <li v-for="suggestion in suggestions" :key="suggestion">{{ suggestion }}</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  password: string
}

const props = defineProps<Props>()

// 计算密码强度
const passwordStrength = computed(() => {
  const password = props.password
  
  if (!password) {
    return { score: 0, text: '', suggestions: [] }
  }
  
  let score = 0
  const suggestions: string[] = []
  
  // 长度检查
  if (password.length >= 8) {
    score += 1
  } else {
    suggestions.push('密码长度至少8位')
  }
  
  if (password.length >= 12) {
    score += 1
  } else if (password.length >= 8) {
    suggestions.push('建议密码长度12位以上')
  }
  
  // 字符类型检查
  if (/[a-z]/.test(password)) {
    score += 1
  } else {
    suggestions.push('包含小写字母')
  }
  
  if (/[A-Z]/.test(password)) {
    score += 1
  } else {
    suggestions.push('包含大写字母')
  }
  
  if (/[0-9]/.test(password)) {
    score += 1
  } else {
    suggestions.push('包含数字')
  }
  
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    score += 1
  } else {
    suggestions.push('包含特殊字符')
  }
  
  // 检查常见弱密码
  const weakPasswords = [
    '123456', 'password', '123456789', '12345678', '12345',
    '1234567', 'admin', '123123', 'qwerty', 'abc123'
  ]
  
  if (weakPasswords.includes(password.toLowerCase())) {
    score = 0
    suggestions.push('避免使用常见密码')
  }
  
  // 检查重复字符
  if (/(.)\1{2,}/.test(password)) {
    score -= 1
    suggestions.push('避免连续重复字符')
  }
  
  // 确保分数在0-6范围内
  score = Math.max(0, Math.min(6, score))
  
  let text = ''
  if (score === 0) text = '很弱'
  else if (score <= 2) text = '弱'
  else if (score <= 4) text = '中等'
  else text = '强'
  
  return { score, text, suggestions }
})

// 强度百分比
const strengthPercentage = computed(() => {
  return (passwordStrength.value.score / 6) * 100
})

// 强度文本
const strengthText = computed(() => {
  return passwordStrength.value.text
})

// 强度样式类
const strengthClass = computed(() => {
  const score = passwordStrength.value.score
  if (score === 0) return 'very-weak'
  if (score <= 2) return 'weak'
  if (score <= 4) return 'medium'
  return 'strong'
})

// 建议列表
const suggestions = computed(() => {
  return passwordStrength.value.suggestions
})
</script>

<style scoped>
.password-strength-meter {
  margin-top: 8px;
}

.strength-bar {
  width: 100%;
  height: 6px;
  background-color: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
  border-radius: 3px;
}

.strength-fill.very-weak {
  background-color: #ef4444;
}

.strength-fill.weak {
  background-color: #f97316;
}

.strength-fill.medium {
  background-color: #eab308;
}

.strength-fill.strong {
  background-color: #22c55e;
}

.strength-text {
  margin-top: 4px;
  font-size: 0.875rem;
}

.strength-text span.very-weak {
  color: #ef4444;
}

.strength-text span.weak {
  color: #f97316;
}

.strength-text span.medium {
  color: #eab308;
}

.strength-text span.strong {
  color: #22c55e;
}

.suggestions {
  margin-top: 8px;
  padding: 8px;
  background-color: #f8fafc;
  border-radius: 4px;
  border-left: 3px solid #3b82f6;
}

.suggestions-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin: 0 0 4px 0;
}

.suggestions ul {
  margin: 0;
  padding-left: 16px;
  font-size: 0.8rem;
  color: #6b7280;
}

.suggestions li {
  margin-bottom: 2px;
}
</style>
