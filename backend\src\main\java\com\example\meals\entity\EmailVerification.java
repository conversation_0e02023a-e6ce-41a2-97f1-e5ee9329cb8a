package com.example.meals.entity;

import java.time.LocalDateTime;

/**
 * 邮箱验证码实体类
 */
public class EmailVerification {
    
    private Long id;
    private String email;           // 邮箱地址
    private String verificationCode; // 验证码
    private String type;            // 验证类型：REGISTER-注册, RESET_PASSWORD-重置密码
    private Integer status;         // 状态：0-未使用，1-已使用，2-已过期
    private LocalDateTime createTime; // 创建时间
    private LocalDateTime expireTime; // 过期时间
    private LocalDateTime useTime;    // 使用时间
    
    // 构造函数
    public EmailVerification() {}
    
    public EmailVerification(String email, String verificationCode, String type, LocalDateTime expireTime) {
        this.email = email;
        this.verificationCode = verificationCode;
        this.type = type;
        this.status = 0; // 默认未使用
        this.createTime = LocalDateTime.now();
        this.expireTime = expireTime;
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getVerificationCode() {
        return verificationCode;
    }
    
    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getExpireTime() {
        return expireTime;
    }
    
    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }
    
    public LocalDateTime getUseTime() {
        return useTime;
    }
    
    public void setUseTime(LocalDateTime useTime) {
        this.useTime = useTime;
    }
    
    @Override
    public String toString() {
        return "EmailVerification{" +
                "id=" + id +
                ", email='" + email + '\'' +
                ", verificationCode='" + verificationCode + '\'' +
                ", type='" + type + '\'' +
                ", status=" + status +
                ", createTime=" + createTime +
                ", expireTime=" + expireTime +
                ", useTime=" + useTime +
                '}';
    }
}
