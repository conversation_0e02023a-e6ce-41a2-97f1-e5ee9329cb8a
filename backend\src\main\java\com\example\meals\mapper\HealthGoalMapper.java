package com.example.meals.mapper;

import com.example.meals.entity.HealthGoal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 健康目标数据访问层
 */
@Mapper
public interface HealthGoalMapper {

    /**
     * 查询所有健康目标
     */
    List<HealthGoal> selectAll();

    /**
     * 查询启用的健康目标
     */
    List<HealthGoal> selectEnabled();

    /**
     * 根据ID查询健康目标
     */
    HealthGoal selectById(Long id);

    /**
     * 根据代码查询健康目标
     */
    HealthGoal selectByCode(String code);

    /**
     * 插入健康目标
     */
    int insert(HealthGoal healthGoal);

    /**
     * 更新健康目标
     */
    int update(HealthGoal healthGoal);

    /**
     * 删除健康目标
     */
    int deleteById(Long id);

    /**
     * 检查代码是否存在（排除指定ID）
     */
    int countByCodeExcludeId(@Param("code") String code, @Param("excludeId") Long excludeId);

    /**
     * 检查代码是否存在
     */
    int countByCode(String code);

    /**
     * 获取最大排序号
     */
    int getMaxSortOrder();
}
