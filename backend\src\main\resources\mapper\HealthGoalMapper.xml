<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.HealthGoalMapper">

    <!-- 结果映射 -->
    <resultMap id="HealthGoalResultMap" type="com.example.meals.entity.HealthGoal">
        <id property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 查询所有健康目标 -->
    <select id="selectAll" resultMap="HealthGoalResultMap">
        SELECT * FROM health_goal 
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 查询启用的健康目标 -->
    <select id="selectEnabled" resultMap="HealthGoalResultMap">
        SELECT * FROM health_goal 
        WHERE status = 1 
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据ID查询健康目标 -->
    <select id="selectById" parameterType="long" resultMap="HealthGoalResultMap">
        SELECT * FROM health_goal WHERE id = #{id}
    </select>

    <!-- 根据代码查询健康目标 -->
    <select id="selectByCode" parameterType="string" resultMap="HealthGoalResultMap">
        SELECT * FROM health_goal WHERE code = #{code}
    </select>

    <!-- 插入健康目标 -->
    <insert id="insert" parameterType="com.example.meals.entity.HealthGoal" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO health_goal (code, name, description, sort_order, status)
        VALUES (#{code}, #{name}, #{description}, #{sortOrder}, #{status})
    </insert>

    <!-- 更新健康目标 -->
    <update id="update" parameterType="com.example.meals.entity.HealthGoal">
        UPDATE health_goal SET
            code = #{code},
            name = #{name},
            description = #{description},
            sort_order = #{sortOrder},
            status = #{status}
        WHERE id = #{id}
    </update>

    <!-- 删除健康目标 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM health_goal WHERE id = #{id}
    </delete>

    <!-- 检查代码是否存在（排除指定ID） -->
    <select id="countByCodeExcludeId" resultType="int">
        SELECT COUNT(*) FROM health_goal 
        WHERE code = #{code} AND id != #{excludeId}
    </select>

    <!-- 检查代码是否存在 -->
    <select id="countByCode" parameterType="string" resultType="int">
        SELECT COUNT(*) FROM health_goal WHERE code = #{code}
    </select>

    <!-- 获取最大排序号 -->
    <select id="getMaxSortOrder" resultType="int">
        SELECT COALESCE(MAX(sort_order), 0) FROM health_goal
    </select>

</mapper>
