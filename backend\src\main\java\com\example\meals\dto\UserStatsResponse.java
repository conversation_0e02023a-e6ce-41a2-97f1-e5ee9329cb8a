package com.example.meals.dto;

/**
 * 用户统计信息响应DTO
 */
public class UserStatsResponse {
    
    private Long totalUsers;        // 用户总数
    private Long activeUsers;       // 启用用户数
    private Long inactiveUsers;     // 禁用用户数
    private Long todayRegistered;   // 今日注册用户数
    
    // 构造函数
    public UserStatsResponse() {}
    
    public UserStatsResponse(Long totalUsers, Long activeUsers, Long inactiveUsers, Long todayRegistered) {
        this.totalUsers = totalUsers;
        this.activeUsers = activeUsers;
        this.inactiveUsers = inactiveUsers;
        this.todayRegistered = todayRegistered;
    }
    
    // Getter 和 Setter 方法
    public Long getTotalUsers() {
        return totalUsers;
    }
    
    public void setTotalUsers(Long totalUsers) {
        this.totalUsers = totalUsers;
    }
    
    public Long getActiveUsers() {
        return activeUsers;
    }
    
    public void setActiveUsers(Long activeUsers) {
        this.activeUsers = activeUsers;
    }
    
    public Long getInactiveUsers() {
        return inactiveUsers;
    }
    
    public void setInactiveUsers(Long inactiveUsers) {
        this.inactiveUsers = inactiveUsers;
    }
    
    public Long getTodayRegistered() {
        return todayRegistered;
    }
    
    public void setTodayRegistered(Long todayRegistered) {
        this.todayRegistered = todayRegistered;
    }
    
    @Override
    public String toString() {
        return "UserStatsResponse{" +
                "totalUsers=" + totalUsers +
                ", activeUsers=" + activeUsers +
                ", inactiveUsers=" + inactiveUsers +
                ", todayRegistered=" + todayRegistered +
                '}';
    }
}
