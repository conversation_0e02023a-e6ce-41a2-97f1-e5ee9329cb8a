package com.example.meals.controller;

import com.example.meals.common.Result;
import com.example.meals.service.FileService;
import com.example.meals.service.UserService;
import com.example.meals.service.AdminService;
import com.example.meals.config.FileUploadConfig;
import com.example.meals.utils.AuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.HttpServletRequest;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/api/files")
public class FileController {

    private static final Logger logger = LoggerFactory.getLogger(FileController.class);

    @Autowired
    private FileService fileService;

    @Autowired
    private UserService userService;

    @Autowired
    private AdminService adminService;

    @Autowired
    private FileUploadConfig fileUploadConfig;
    
    /**
     * 上传用户头像
     */
    @PostMapping("/avatar/user")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> uploadUserAvatar(@RequestParam("file") MultipartFile file,
                                         HttpServletRequest request) {
        Long userId = null;
        String uploadedFilePath = null;

        try {
            // 验证用户权限
            userId = AuthUtil.getCurrentUserId(request);
            if (userId == null) {
                logger.warn("未登录用户尝试上传头像");
                return Result.unauthorized("用户未登录");
            }
            String userType = (String) request.getAttribute("userType");

            if (!"USER".equals(userType)) {
                logger.warn("用户ID: {} 权限不足，尝试访问用户头像上传接口", userId);
                return Result.forbidden("权限不足，仅普通用户可访问");
            }

            logger.info("用户ID: {} 开始上传头像，文件大小: {} bytes", userId, file.getSize());

            // 获取用户当前头像路径（用于删除旧头像）
            String oldAvatarPath = userService.getUserAvatarPath(userId);

            // 上传文件
            Result<String> uploadResult = fileService.uploadAvatar(file, userId, "USER");
            if (!uploadResult.getSuccess()) {
                logger.error("用户ID: {} 头像上传失败: {}", userId, uploadResult.getMessage());
                return uploadResult;
            }

            uploadedFilePath = uploadResult.getData();

            // 更新用户头像路径
            Result<Void> updateResult = userService.updateUserAvatar(userId, uploadedFilePath);
            if (!updateResult.getSuccess()) {
                logger.error("用户ID: {} 头像路径更新失败: {}", userId, updateResult.getMessage());
                // 如果更新失败，删除已上传的文件
                fileService.deleteAvatar(uploadedFilePath);
                return Result.error("头像更新失败：" + updateResult.getMessage());
            }

            // 删除旧头像文件（如果存在）
            if (oldAvatarPath != null && !oldAvatarPath.trim().isEmpty()) {
                Result<Void> deleteResult = fileService.deleteAvatar(oldAvatarPath);
                if (!deleteResult.getSuccess()) {
                    logger.warn("用户ID: {} 旧头像删除失败: {}", userId, deleteResult.getMessage());
                }
            }

            // 返回可访问的URL路径
            String fileUrl = fileService.getFileUrl(uploadedFilePath);
            logger.info("用户ID: {} 头像上传成功，文件路径: {}", userId, fileUrl);
            return Result.success("头像上传成功", fileUrl);

        } catch (Exception e) {
            logger.error("用户ID: {} 头像上传过程中发生异常", userId, e);
            // 清理可能已上传的文件
            if (uploadedFilePath != null) {
                fileService.deleteAvatar(uploadedFilePath);
            }
            return Result.error("头像上传失败，请重试");
        }
    }
    
    /**
     * 上传管理员头像
     */
    @PostMapping("/avatar/admin")
    public Result<String> uploadAdminAvatar(@RequestParam("file") MultipartFile file,
                                          HttpServletRequest request) {
        // 验证管理员权限
        Long adminId = AuthUtil.getCurrentUserId(request);
        if (adminId == null) {
            return Result.unauthorized("用户未登录");
        }
        String userType = (String) request.getAttribute("userType");

        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }

        // 获取管理员当前头像路径（用于删除旧头像）
        String oldAvatarPath = adminService.getAdminAvatarPath(adminId);

        // 上传文件
        Result<String> uploadResult = fileService.uploadAvatar(file, adminId, "ADMIN");
        if (!uploadResult.getSuccess()) {
            return uploadResult;
        }

        // 更新管理员头像路径
        Result<Void> updateResult = adminService.updateAdminAvatar(adminId, uploadResult.getData());
        if (!updateResult.getSuccess()) {
            // 如果更新失败，删除已上传的文件
            fileService.deleteAvatar(uploadResult.getData());
            return Result.error("头像更新失败：" + updateResult.getMessage());
        }

        // 删除旧头像文件（如果存在）
        if (oldAvatarPath != null && !oldAvatarPath.trim().isEmpty()) {
            fileService.deleteAvatar(oldAvatarPath);
        }

        // 返回可访问的URL路径
        String fileUrl = fileService.getFileUrl(uploadResult.getData());
        return Result.success("头像上传成功", fileUrl);
    }
    
    /**
     * 获取文件资源
     */
    @GetMapping("/uploads/**")
    public ResponseEntity<Resource> getFile(HttpServletRequest request) {
        try {
            // 获取文件路径
            String requestURI = request.getRequestURI();
            String relativePath = requestURI.substring("/api/files/uploads/".length());

            // 安全检查：防止路径遍历攻击
            if (relativePath.contains("..") || relativePath.contains("\\..") ||
                relativePath.contains("../") || relativePath.contains("..\\")) {
                return ResponseEntity.badRequest().build();
            }

            // 只允许访问头像目录下的文件
            if (!relativePath.startsWith("avatars/")) {
                return ResponseEntity.status(403).build();
            }

            // 构建完整的文件路径
            String basePath = fileUploadConfig.getPath();
            if (!basePath.endsWith("/") && !basePath.endsWith("\\")) {
                basePath += "/";
            }
            String fullPath = basePath + relativePath;

            Path file = Paths.get(fullPath);

            // 确保文件在允许的目录内
            Path allowedDir = Paths.get(fileUploadConfig.getPath()).toAbsolutePath().normalize();
            Path requestedFile = file.toAbsolutePath().normalize();
            if (!requestedFile.startsWith(allowedDir)) {
                return ResponseEntity.status(403).build();
            }

            Resource resource = new UrlResource(requestedFile.toUri());

            if (resource.exists() && resource.isReadable()) {
                // 确定文件类型
                String contentType = "application/octet-stream";
                String filename = requestedFile.getFileName().toString().toLowerCase();

                if (filename.endsWith(".jpg") || filename.endsWith(".jpeg")) {
                    contentType = "image/jpeg";
                } else if (filename.endsWith(".png")) {
                    contentType = "image/png";
                } else if (filename.endsWith(".gif")) {
                    contentType = "image/gif";
                } else {
                    // 只允许图片文件访问
                    return ResponseEntity.badRequest().build();
                }

                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CACHE_CONTROL, "max-age=3600")
                        .header("Content-Security-Policy", "default-src 'none'")
                        .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 删除头像
     */
    @DeleteMapping("/avatar")
    public Result<Void> deleteAvatar(HttpServletRequest request) {
        // 验证用户权限
        Long userId = AuthUtil.getCurrentUserId(request);
        if (userId == null) {
            return Result.unauthorized("用户未登录");
        }
        String userType = (String) request.getAttribute("userType");
        
        if ("USER".equals(userType)) {
            return userService.deleteUserAvatar(userId);
        } else if ("ADMIN".equals(userType)) {
            return adminService.deleteAdminAvatar(userId);
        } else {
            return Result.forbidden("权限不足");
        }
    }
}
