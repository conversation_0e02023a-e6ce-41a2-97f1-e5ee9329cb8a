import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import { setupGlobalErrorHandling, setupNetworkStatusMonitoring } from './utils/errorHandler'
import { AuthManager } from './utils/auth'

const app = createApp(App)

// 设置全局错误处理
setupGlobalErrorHandling()
setupNetworkStatusMonitoring()

// 初始化认证状态
AuthManager.initializeAuth()

app.use(createPinia())
app.use(router)

app.mount('#app')
