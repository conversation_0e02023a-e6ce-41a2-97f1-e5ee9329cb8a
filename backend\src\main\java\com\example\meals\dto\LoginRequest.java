package com.example.meals.dto;

/**
 * 登录请求DTO
 */
public class LoginRequest {
    
    private String emailOrPhone; // 邮箱或手机号
    private String password;     // 密码
    private Boolean rememberMe;  // 记住我

    // 构造函数
    public LoginRequest() {}

    public LoginRequest(String emailOrPhone, String password) {
        this.emailOrPhone = emailOrPhone;
        this.password = password;
        this.rememberMe = false;
    }

    public LoginRequest(String emailOrPhone, String password, Boolean rememberMe) {
        this.emailOrPhone = emailOrPhone;
        this.password = password;
        this.rememberMe = rememberMe;
    }
    
    // Getter 和 Setter 方法
    public String getEmailOrPhone() {
        return emailOrPhone;
    }
    
    public void setEmailOrPhone(String emailOrPhone) {
        this.emailOrPhone = emailOrPhone;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }

    public Boolean getRememberMe() {
        return rememberMe;
    }

    public void setRememberMe(Boolean rememberMe) {
        this.rememberMe = rememberMe;
    }

    @Override
    public String toString() {
        return "LoginRequest{" +
                "emailOrPhone='" + emailOrPhone + '\'' +
                ", password='[PROTECTED]'" +
                ", rememberMe=" + rememberMe +
                '}';
    }
}
