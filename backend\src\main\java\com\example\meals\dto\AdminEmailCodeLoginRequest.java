package com.example.meals.dto;

/**
 * 管理员邮箱验证码登录请求DTO
 */
public class AdminEmailCodeLoginRequest {
    
    private String email;            // 邮箱地址
    private String verificationCode; // 验证码
    
    // 构造函数
    public AdminEmailCodeLoginRequest() {}
    
    public AdminEmailCodeLoginRequest(String email, String verificationCode) {
        this.email = email;
        this.verificationCode = verificationCode;
    }
    
    // Getter 和 Setter 方法
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getVerificationCode() {
        return verificationCode;
    }
    
    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }
    
    @Override
    public String toString() {
        return "AdminEmailCodeLoginRequest{" +
                "email='" + email + '\'' +
                ", verificationCode='[PROTECTED]'" +
                '}';
    }
}
