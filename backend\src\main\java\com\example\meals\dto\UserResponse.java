package com.example.meals.dto;

import com.example.meals.entity.User;
import java.time.LocalDateTime;

/**
 * 用户响应DTO（不包含敏感信息）
 */
public class UserResponse {

    private Long id;
    private String username;
    private String email;
    private String avatar; // 头像文件路径
    private Integer status;
    private String statusName; // 状态名称
    private LocalDateTime createTime;
    private String token; // JWT Token

    // 个人资料扩展字段
    private String realName; // 真实姓名
    private String phone; // 手机号码
    private Integer gender; // 性别：0-未知，1-男，2-女
    private String genderName; // 性别名称
    private String birthday; // 生日
    private Integer age; // 年龄
    private Double height; // 身高(cm)
    private Double weight; // 体重(kg)
    private String allergies; // 过敏信息
    private String dietaryPreferences; // 饮食偏好
    private String healthGoals; // 健康目标
    private String bio; // 个人简介
    
    // 构造函数
    public UserResponse() {}
    
    public UserResponse(User user) {
        this.id = user.getId();
        this.username = user.getUsername();
        this.email = user.getEmail();
        this.avatar = user.getAvatar();
        this.status = user.getStatus();
        this.statusName = user.getStatus() == 1 ? "启用" : "禁用";
        this.createTime = user.getCreateTime();

        // 个人资料扩展字段
        this.realName = user.getRealName();
        this.phone = user.getPhone();
        this.gender = user.getGender();
        this.genderName = getGenderName(user.getGender());
        this.birthday = user.getBirthday();
        this.age = user.getAge();
        this.height = user.getHeight();
        this.weight = user.getWeight();
        this.allergies = user.getAllergies();
        this.dietaryPreferences = user.getDietaryPreferences();
        this.healthGoals = user.getHealthGoals();
        this.bio = user.getBio();
    }

    // 性别名称转换
    private String getGenderName(Integer gender) {
        if (gender == null) return "未知";
        switch (gender) {
            case 1: return "男";
            case 2: return "女";
            default: return "未知";
        }
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
        this.statusName = status == 1 ? "启用" : "禁用";
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    // 个人资料扩展字段的getter和setter方法
    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getGenderName() {
        return genderName;
    }

    public void setGenderName(String genderName) {
        this.genderName = genderName;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public String getAllergies() {
        return allergies;
    }

    public void setAllergies(String allergies) {
        this.allergies = allergies;
    }

    public String getDietaryPreferences() {
        return dietaryPreferences;
    }

    public void setDietaryPreferences(String dietaryPreferences) {
        this.dietaryPreferences = dietaryPreferences;
    }

    public String getHealthGoals() {
        return healthGoals;
    }

    public void setHealthGoals(String healthGoals) {
        this.healthGoals = healthGoals;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    @Override
    public String toString() {
        return "UserResponse{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", realName='" + realName + '\'' +
                ", status=" + status +
                ", createTime=" + createTime +
                '}';
    }
}
