<template>
  <nav class="top-navbar">
    <div class="nav-content">
      <!-- 左侧品牌区域 -->
      <div class="nav-brand">
        <!-- 如果有返回链接，显示返回按钮 -->
        <router-link v-if="backTo" :to="backTo" class="back-link">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          {{ backText || '返回' }}
        </router-link>
        <!-- 否则显示品牌logo和名称 -->
        <template v-else>
          <div class="brand-logo">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                    fill="currentColor"/>
              <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <span class="brand-name">膳食营养分析平台</span>
        </template>
      </div>

      <!-- 中间标题区域 -->
      <h1 v-if="pageTitle" class="page-title">{{ pageTitle }}</h1>

      <!-- 右侧操作区域 -->
      <div class="nav-actions">
        <!-- 自定义操作插槽 -->
        <slot name="actions"></slot>
        
        <!-- 默认用户信息和操作 -->
        <template v-if="showUserInfo && isLoggedIn">
          <span class="welcome-text">欢迎，{{ user?.username }}</span>
          
          <!-- 用户头像（仅在控制台页面显示） -->
          <div v-if="showAvatar" class="user-avatar" @click="$emit('avatar-click')">
            <AvatarDisplay
              :avatar-url="getAvatarUrl(user?.avatar)"
              :name="user?.username"
              :size="36"
              :clickable="false"
              :show-upload-overlay="true"
            />
          </div>
          
          <!-- 控制台/管理后台按钮 -->
          <router-link
            v-if="showDashboardLink"
            :to="user?.userType === 'ADMIN' ? '/admin/dashboard' : '/dashboard'"
            class="nav-btn dashboard-btn">
            {{ user?.userType === 'ADMIN' ? '管理后台' : '控制台' }}
          </router-link>
          
          <!-- 退出登录按钮 -->
          <button v-if="showLogoutButton" @click="$emit('logout')" class="nav-btn logout-btn">
            退出登录
          </button>
        </template>
        
        <!-- 未登录状态的登录按钮 -->
        <template v-else-if="showUserInfo && !isLoggedIn">
          <router-link to="/login" class="nav-btn login-btn">登录</router-link>
          <router-link to="/register" class="nav-btn register-btn">注册</router-link>
        </template>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuth } from '../composables/useAuth'
import AvatarDisplay from '../components/AvatarDisplay.vue'
import { getAvatarUrl } from '../config/api'

// Props 定义
interface Props {
  // 页面标题
  pageTitle?: string
  // 返回链接
  backTo?: string
  // 返回按钮文本
  backText?: string
  // 是否显示用户信息
  showUserInfo?: boolean
  // 是否显示用户头像
  showAvatar?: boolean
  // 是否显示控制台链接
  showDashboardLink?: boolean
  // 是否显示退出登录按钮
  showLogoutButton?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showUserInfo: true,
  showAvatar: false,
  showDashboardLink: false,
  showLogoutButton: false
})

// 事件定义
const emit = defineEmits<{
  'avatar-click': []
  'logout': []
}>()

// 使用认证状态
const { user, isLoggedIn } = useAuth()

// 使用统一的头像URL处理方法
</script>

<style scoped>
/* 顶部导航栏样式 */
.top-navbar {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  z-index: 100;
  padding: 1rem 0;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 品牌区域 */
.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-logo {
  width: 40px;
  height: 40px;
  color: var(--primary-color, #3b82f6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
}

/* 返回链接 */
.back-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color, #3b82f6);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.back-link:hover {
  color: var(--primary-color-dark, #2563eb);
}

.back-link svg {
  width: 20px;
  height: 20px;
}

/* 页面标题 */
.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  margin: 0;
}

/* 操作区域 */
.nav-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.welcome-text {
  color: var(--text-secondary, #6b7280);
  font-size: 0.9rem;
}

/* 用户头像 */
.user-avatar {
  cursor: pointer;
  transition: transform 0.2s ease;
  position: relative;
  z-index: 100;
}

.user-avatar:hover {
  transform: scale(1.05);
}

/* 导航按钮 */
.nav-btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md, 0.5rem);
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-family: inherit;
}

.login-btn {
  background: transparent;
  color: var(--primary-color, #3b82f6);
  border: 1px solid var(--primary-color, #3b82f6);
}

.login-btn:hover {
  background: var(--primary-color, #3b82f6);
  color: white;
}

.register-btn {
  background: var(--primary-color, #3b82f6);
  color: white;
  border: 1px solid var(--primary-color, #3b82f6);
}

.register-btn:hover {
  background: var(--primary-color-dark, #2563eb);
  border-color: var(--primary-color-dark, #2563eb);
}

.dashboard-btn {
  background: var(--primary-color, #3b82f6);
  color: white;
  border: 1px solid var(--primary-color, #3b82f6);
}

.dashboard-btn:hover {
  background: var(--primary-color-dark, #2563eb);
  border-color: var(--primary-color-dark, #2563eb);
}

.logout-btn {
  background: #e74c3c;
  color: white !important;
  border: 2px solid #e74c3c;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.logout-btn:hover {
  background: #c0392b;
  border-color: #c0392b;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 自定义操作按钮样式 */
.home-btn {
  background: linear-gradient(135deg, var(--primary-color, #3b82f6) 0%, var(--primary-color-dark, #2563eb) 100%);
  color: white;
  border: 1px solid transparent;
}

.home-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.28);
}

/* 保存按钮样式 */
.save-btn {
  background: #16a085;
  color: white !important;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md, 0.5rem);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn:hover:not(:disabled) {
  background: #138d75;
  transform: translateY(-1px);
}

.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-content {
    padding: 0 1rem;
  }
  
  .brand-name {
    display: none;
  }
  
  .welcome-text {
    display: none;
  }
  
  .nav-actions {
    gap: 0.5rem;
  }
  
  .nav-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}
</style>
