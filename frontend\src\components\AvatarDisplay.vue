<template>
  <div
    class="avatar-display"
    :class="{ clickable: clickable }"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div class="avatar-container" :style="{ width: size + 'px', height: size + 'px' }">
      <!-- 自定义头像 -->
      <img
        v-if="avatarUrl"
        :src="avatarUrl"
        :alt="name || 'Avatar'"
        class="avatar-image"
        @error="handleImageError"
      />
      <!-- 字母头像 -->
      <div
        v-else
        class="avatar-letter"
        :style="{
          backgroundColor: letterAvatarColor,
          fontSize: letterFontSize + 'px',
          lineHeight: size + 'px'
        }"
      >
        {{ avatarLetter }}
      </div>
      
      <!-- 上传按钮覆盖层 -->
      <div v-if="showUploadOverlay" class="upload-overlay">
        <i class="upload-icon">📷</i>
      </div>
    </div>

    <!-- 状态指示器 -->
    <div v-if="showStatus" class="status-indicator" :class="statusClass"></div>

  </div>

  <!-- 悬停预览 - 使用Teleport渲染到body确保正确显示 -->
  <Teleport to="body">
    <div
      v-if="showPreview && avatarUrl"
      class="avatar-preview"
      :style="previewStyle"
    >
      <img
        :src="avatarUrl"
        :alt="name || 'Avatar Preview'"
        class="preview-image"
        @load="handlePreviewLoad"
        @error="handlePreviewError"
      />
    </div>
  </Teleport>
</template>

<script>
export default {
  name: 'AvatarDisplay',
  props: {
    // 头像URL
    avatarUrl: {
      type: String,
      default: null
    },
    // 用户名或真实姓名
    name: {
      type: String,
      default: ''
    },
    // 头像尺寸
    size: {
      type: Number,
      default: 40
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      default: false
    },
    // 是否显示上传覆盖层
    showUploadOverlay: {
      type: Boolean,
      default: false
    },
    // 是否显示状态指示器
    showStatus: {
      type: Boolean,
      default: false
    },
    // 状态类型 (online, offline, busy)
    status: {
      type: String,
      default: 'offline'
    }
  },
  data() {
    return {
      showPreview: false,
      hoverTimer: null,
      previewPosition: { x: 0, y: 0 },
      clickDisableTimer: null,
      isClickDisabled: false
    }
  },
  computed: {
    // 字母头像显示的字符
    avatarLetter() {
      if (!this.name) return '?'
      
      // 如果是中文名，取最后一个字符
      if (/[\u4e00-\u9fa5]/.test(this.name)) {
        return this.name.charAt(this.name.length - 1)
      }
      
      // 如果是英文名，取第一个字符
      return this.name.charAt(0).toUpperCase()
    },
    
    // 字母头像背景色
    letterAvatarColor() {
      const colors = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
      ]
      
      if (!this.name) return colors[0]
      
      // 根据名字生成固定的颜色
      let hash = 0
      for (let i = 0; i < this.name.length; i++) {
        hash = this.name.charCodeAt(i) + ((hash << 5) - hash)
      }
      
      return colors[Math.abs(hash) % colors.length]
    },
    
    // 字母字体大小
    letterFontSize() {
      return Math.floor(this.size * 0.4)
    },
    
    // 状态指示器样式类
    statusClass() {
      return `status-${this.status}`
    },

    // 预览样式
    previewStyle() {
      return {
        left: this.previewPosition.x + 'px',
        top: this.previewPosition.y + 'px'
      }
    }
  },
  methods: {
    handleClick(event) {
      // 立即清除悬停定时器和隐藏预览
      this.clearHoverTimer()
      this.showPreview = false

      // 设置点击禁用标志，防止点击后立即显示预览
      this.isClickDisabled = true

      // 清除之前的禁用定时器
      if (this.clickDisableTimer) {
        clearTimeout(this.clickDisableTimer)
      }

      // 2秒后重新启用预览功能
      this.clickDisableTimer = setTimeout(() => {
        this.isClickDisabled = false
      }, 2000)

      if (this.clickable) {
        if (event && event.stopPropagation) {
          event.stopPropagation()
        }
        this.$emit('click')
      }
    },

    handleImageError() {
      // 图片加载失败时的处理
      this.$emit('image-error')
    },

    // 鼠标进入事件
    handleMouseEnter(event) {
      // 只有当有头像URL时才显示预览
      if (!this.avatarUrl) return

      // 如果点击后禁用期间，不显示预览
      if (this.isClickDisabled) return

      // 清除之前的定时器
      this.clearHoverTimer()

      // 保存当前元素引用
      const currentTarget = event.currentTarget

      // 设置1秒延迟
      this.hoverTimer = setTimeout(() => {
        // 检查元素是否仍然存在且可见，且未被点击禁用
        if (currentTarget && currentTarget.isConnected &&
            this.showPreview !== true && !this.isClickDisabled) {
          this.calculatePreviewPosition(currentTarget)
          this.showPreview = true
        }
      }, 1000)
    },

    // 鼠标离开事件
    handleMouseLeave() {
      // 清除悬停定时器
      this.clearHoverTimer()

      // 立即隐藏预览
      this.showPreview = false
    },

    // 计算预览位置
    calculatePreviewPosition(element) {
      try {
        // 安全检查：确保元素存在且已连接到DOM
        if (!element || !element.isConnected) {
          console.warn('Element not available for preview positioning')
          return
        }

        const rect = element.getBoundingClientRect()

        // 检查rect是否有效
        if (!rect || rect.width === 0 || rect.height === 0) {
          console.warn('Invalid element bounds for preview positioning')
          return
        }

        const previewSize = 300 // 预览图片大小
        const offset = 20 // 偏移量

        // 默认显示在右侧
        let x = rect.right + offset
        let y = rect.top

        // 如果右侧空间不够，显示在左侧
        if (x + previewSize > window.innerWidth) {
          x = rect.left - previewSize - offset
        }

        // 如果下方空间不够，向上调整
        if (y + previewSize > window.innerHeight) {
          y = window.innerHeight - previewSize - offset
        }

        // 确保不超出屏幕边界
        x = Math.max(offset, x)
        y = Math.max(offset, y)

        this.previewPosition = { x, y }
      } catch (error) {
        console.error('Error calculating preview position:', error)
        // 发生错误时隐藏预览
        this.showPreview = false
      }
    },

    // 预览图片加载成功
    handlePreviewLoad() {
      // 预览图片加载成功的处理
    },

    // 预览图片加载失败
    handlePreviewError() {
      // 预览图片加载失败时隐藏预览
      this.showPreview = false
    },

    // 清理悬停定时器
    clearHoverTimer() {
      if (this.hoverTimer) {
        clearTimeout(this.hoverTimer)
        this.hoverTimer = null
      }
    },

    // 清理点击禁用定时器
    clearClickDisableTimer() {
      if (this.clickDisableTimer) {
        clearTimeout(this.clickDisableTimer)
        this.clickDisableTimer = null
      }
    },

    // 清理所有资源
    cleanup() {
      this.clearHoverTimer()
      this.clearClickDisableTimer()
      this.showPreview = false
      this.isClickDisabled = false
    }
  },

  // 组件销毁时清理定时器
  beforeUnmount() {
    this.cleanup()
  },

  beforeDestroy() {
    // Vue 2 兼容
    this.cleanup()
  }
}
</script>

<style scoped>
.avatar-display {
  position: relative;
  display: inline-block;
}

.avatar-display.clickable {
  cursor: pointer;
}

.avatar-container {
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e1e5e9;
  transition: all 0.3s ease;
}

.avatar-display.clickable .avatar-container:hover {
  border-color: #007bff;
  transform: scale(1.05);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.avatar-letter {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  text-align: center;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-display.clickable:hover .upload-overlay {
  opacity: 1;
}

.upload-icon {
  font-size: 20px;
  color: white;
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-online {
  background-color: #28a745;
}

.status-offline {
  background-color: #6c757d;
}

.status-busy {
  background-color: #dc3545;
}

/* 悬停预览样式 */
.avatar-preview {
  position: fixed;
  z-index: 9999;
  width: 300px;
  height: 300px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 2px solid #e0e0e0;
  overflow: hidden;
  animation: fadeInScale 0.2s ease-out;
  pointer-events: none; /* 防止预览图片干扰鼠标事件 */
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #f8f9fa;
}

/* 预览动画 */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-overlay {
    opacity: 1;
  }

  /* 移动端预览尺寸调整 */
  .avatar-preview {
    width: 250px;
    height: 250px;
  }
}

/* 小屏幕设备预览调整 */
@media (max-width: 480px) {
  .avatar-preview {
    width: 200px;
    height: 200px;
  }
}
</style>
