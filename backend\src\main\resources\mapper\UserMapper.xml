<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.UserMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.meals.entity.User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <!-- 个人资料扩展字段 -->
        <result column="real_name" property="realName"/>
        <result column="phone" property="phone"/>
        <result column="gender" property="gender"/>
        <result column="birthday" property="birthday"/>
        <result column="age" property="age"/>
        <result column="height" property="height"/>
        <result column="weight" property="weight"/>
        <result column="allergies" property="allergies" jdbcType="VARCHAR"/>
        <result column="dietary_preferences" property="dietaryPreferences" jdbcType="VARCHAR"/>
        <result column="health_goals" property="healthGoals" jdbcType="VARCHAR"/>
        <result column="bio" property="bio" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, username, password, email, avatar, status, create_time, update_time,
        real_name, phone, gender, birthday, age, height, weight, allergies,
        dietary_preferences, health_goals, bio
    </sql>

    <!-- 根据ID查询用户 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE id = #{id}
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE username = #{username}
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE email = #{email}
    </select>



    <!-- 插入用户 -->
    <insert id="insert" parameterType="com.example.meals.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user (username, password, email, avatar, status, create_time, update_time)
        VALUES (#{username}, #{password}, #{email}, #{avatar}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <!-- 更新用户信息 -->
    <update id="updateById" parameterType="com.example.meals.entity.User">
        UPDATE user
        <set>
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="email != null">email = #{email},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="status != null">status = #{status},</if>
            update_time = #{updateTime}
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除用户 -->
    <delete id="deleteById">
        DELETE FROM user WHERE id = #{id}
    </delete>

    <!-- 检查用户名是否存在 -->
    <select id="countByUsername" resultType="int">
        SELECT COUNT(*) FROM user WHERE username = #{username}
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="countByEmail" resultType="int">
        SELECT COUNT(*) FROM user WHERE email = #{email}
    </select>



    <!-- 分页查询用户列表（管理员功能） -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据条件统计用户总数（管理员功能） -->
    <select id="countByCondition" resultType="int">
        SELECT COUNT(*)
        FROM user
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

    <!-- 统计用户总数 -->
    <select id="countAll" resultType="int">
        SELECT COUNT(*) FROM user
    </select>

    <!-- 统计今日活跃用户数（简化实现：今日注册用户数） -->
    <select id="countTodayActive" resultType="int">
        SELECT COUNT(*)
        FROM user
        WHERE DATE(create_time) = CURDATE()
    </select>

    <!-- 更新用户资料信息 -->
    <update id="updateProfile" parameterType="com.example.meals.entity.User">
        UPDATE user
        <set>
            <if test="realName != null and realName != ''">real_name = #{realName},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="birthday != null and birthday != ''">birthday = #{birthday},</if>
            <if test="age != null">age = #{age},</if>
            <if test="height != null">height = #{height},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="allergies != null and allergies != ''">allergies = #{allergies},</if>
            <if test="dietaryPreferences != null and dietaryPreferences != ''">dietary_preferences = #{dietaryPreferences},</if>
            <if test="healthGoals != null and healthGoals != ''">health_goals = #{healthGoals},</if>
            <if test="bio != null and bio != ''">bio = #{bio},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

</mapper>
