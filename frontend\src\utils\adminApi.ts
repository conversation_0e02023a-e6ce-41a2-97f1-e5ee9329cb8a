// 管理员相关API接口

import { authFetch, apiRequest, AuthManager, TokenManager } from './auth'

// 基础配置
const API_BASE_URL = 'http://localhost:8080/api/admin'

// 包装函数，将apiRequest的结果包装成ApiResponse格式
async function makeAuthenticatedRequest<T>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
  const response = await authFetch(url, options)
  return response.json()
}

// 类型定义
export interface AdminLoginRequest {
  username: string
  password: string
}



export interface AdminEmailCodeLoginRequest {
  email: string
  verificationCode: string
}



export interface Admin {
  id: number
  username: string
  email: string
  realName: string
  avatar?: string
  roleName: string
  status: number
  statusName: string
  createTime: string
  lastLoginTime?: string
  token?: string  // JWT Token，登录时返回
}

export interface ApiResponse<T> {
  code: number
  message: string
  data?: T
  success: boolean
}

export interface PageResponse<T> {
  records: T[]
  total: number
  page: number
  size: number
  pages: number
}



// 管理员登录
export async function adminLogin(data: AdminLoginRequest): Promise<ApiResponse<Admin>> {
  const response = await fetch(`${API_BASE_URL}/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  const result = await response.json()

  // 如果登录成功，保存token和用户信息
  if (result.success && result.data) {
    const { token, ...userData } = result.data
    if (token) {
      AuthManager.login(token, { ...userData, userType: 'ADMIN' })
    }
  }

  return result
}

// 管理员邮箱验证码登录
export async function adminLoginWithCode(data: AdminEmailCodeLoginRequest): Promise<ApiResponse<Admin>> {
  const response = await fetch(`${API_BASE_URL}/login-with-code`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  const result = await response.json()

  // 如果登录成功，保存token和用户信息
  if (result.success && result.data) {
    const { token, ...userData } = result.data
    if (token) {
      AuthManager.login(token, { ...userData, userType: 'ADMIN' })
    }
  }

  return result
}



// 发送管理员登录验证码
export async function sendAdminLoginCode(email: string): Promise<ApiResponse<void>> {
  const response = await fetch(`${API_BASE_URL}/send-login-code?email=${encodeURIComponent(email)}`, {
    method: 'POST',
  })

  return response.json()
}


// 删除管理员（需要认证）
export async function deleteAdmin(id: number): Promise<ApiResponse<void>> {
  return makeAuthenticatedRequest<void>(`${API_BASE_URL}/${id}`, {
    method: 'DELETE',
  })
}

// 获取管理员信息（需要认证）
export async function getAdminById(id: number): Promise<ApiResponse<Admin>> {
  return makeAuthenticatedRequest<Admin>(`${API_BASE_URL}/${id}`)
}

// 获取管理员列表
export async function getAdminList(params: {
  page?: number
  size?: number
  username?: string
  realName?: string
  status?: number
}): Promise<ApiResponse<PageResponse<Admin>>> {
  const searchParams = new URLSearchParams()

  if (params.page) searchParams.append('page', params.page.toString())
  if (params.size) searchParams.append('size', params.size.toString())
  if (params.username) searchParams.append('username', params.username)
  if (params.realName) searchParams.append('realName', params.realName)
  if (params.status !== undefined) searchParams.append('status', params.status.toString())

  return makeAuthenticatedRequest<PageResponse<Admin>>(`${API_BASE_URL}/list?${searchParams.toString()}`)
}

// 启用/禁用管理员（需要认证）
export async function toggleAdminStatus(id: number, status: number): Promise<ApiResponse<void>> {
  return makeAuthenticatedRequest<void>(`${API_BASE_URL}/${id}/status?status=${status}`, {
    method: 'PUT',
  })
}

// 重置密码（需要认证）
export async function resetPassword(id: number, newPassword: string): Promise<ApiResponse<void>> {
  return makeAuthenticatedRequest<void>(`${API_BASE_URL}/${id}/reset-password?newPassword=${encodeURIComponent(newPassword)}`, {
    method: 'PUT',
  })
}

// 管理员登出
export async function adminLogout(): Promise<void> {
  AuthManager.logout()
}



// ==================== 用户管理API ====================

// 用户相关类型定义
export interface User {
  id: number
  username: string
  email: string
  status: number
  statusName: string
  createTime: string
}

export interface UserStatsResponse {
  totalUsers: number
  activeUsers: number
  inactiveUsers: number
  todayRegistered: number
}

// 获取用户列表
export async function getUserList(params: {
  page?: number
  size?: number
  username?: string
  email?: string
  phone?: string
  status?: number
}): Promise<ApiResponse<PageResponse<User>>> {
  const searchParams = new URLSearchParams()

  if (params.page) searchParams.append('page', params.page.toString())
  if (params.size) searchParams.append('size', params.size.toString())
  if (params.username) searchParams.append('username', params.username)
  if (params.email) searchParams.append('email', params.email)
  if (params.phone) searchParams.append('phone', params.phone)
  if (params.status !== undefined) searchParams.append('status', params.status.toString())

  return makeAuthenticatedRequest<PageResponse<User>>(`${API_BASE_URL}/users?${searchParams.toString()}`)
}

// 切换用户状态
export async function toggleUserStatus(id: number, status: number): Promise<ApiResponse<void>> {
  return makeAuthenticatedRequest<void>(`${API_BASE_URL}/users/${id}/status?status=${status}`, {
    method: 'PUT'
  })
}

// 删除用户
export async function deleteUser(id: number): Promise<ApiResponse<void>> {
  return makeAuthenticatedRequest<void>(`${API_BASE_URL}/users/${id}`, {
    method: 'DELETE'
  })
}

// 获取用户统计信息
export async function getUserStats(): Promise<ApiResponse<UserStatsResponse>> {
  return makeAuthenticatedRequest<UserStatsResponse>(`${API_BASE_URL}/users/stats`)
}

// 上传管理员头像
export async function uploadAdminAvatar(file: File): Promise<ApiResponse<string>> {
  const formData = new FormData()
  formData.append('file', file)

  // {{ AURA-X: Modify - 使用统一API配置. Approval: 寸止(ID:1735555200). }}
  const response = await fetch(`${API_BASE_URL}/api/files/avatar/admin`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${TokenManager.getToken()}`
    },
    body: formData
  })

  return response.json()
}

// 删除管理员头像
export async function deleteAdminAvatar(): Promise<ApiResponse<void>> {
  // {{ AURA-X: Modify - 使用TokenManager获取token. Approval: 寸止(ID:1735555200). }}
  const response = await fetch('http://localhost:8080/api/files/avatar', {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${TokenManager.getToken()}`,
      'Content-Type': 'application/json'
    }
  })

  return response.json()
}

// 导入统一的头像URL处理函数
import { getAvatarUrl } from '../config/api'

// 重新导出以保持API兼容性
export { getAvatarUrl }
