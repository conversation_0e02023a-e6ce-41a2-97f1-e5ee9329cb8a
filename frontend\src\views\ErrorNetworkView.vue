<template>
  <div class="error-container">
    <!-- 顶部导航栏 -->
    <TopNavbar
      :show-logout-button="true"
      @logout="handleLogout"
    >
      <template #actions>
        <router-link to="/" class="nav-btn home-btn">返回首页</router-link>
      </template>
    </TopNavbar>

    <!-- 错误内容区域 -->
    <main class="error-main">
      <div class="error-content">
        <!-- 错误图标 -->
        <div class="error-icon">
          <svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
            <!-- 网络断开图标 -->
            <circle cx="100" cy="100" r="80" stroke="currentColor" stroke-width="3" fill="none" opacity="0.3"/>
            <!-- WiFi信号图标（断开） -->
            <path d="M60 120 Q100 80 140 120" stroke="currentColor" stroke-width="4" fill="none" opacity="0.5"/>
            <path d="M70 130 Q100 100 130 130" stroke="currentColor" stroke-width="4" fill="none" opacity="0.7"/>
            <path d="M80 140 Q100 120 120 140" stroke="currentColor" stroke-width="4" fill="none"/>
            <!-- 断开符号 -->
            <line x1="85" y1="85" x2="115" y2="115" stroke="#e74c3c" stroke-width="6" stroke-linecap="round"/>
            <line x1="115" y1="85" x2="85" y2="115" stroke="#e74c3c" stroke-width="6" stroke-linecap="round"/>
            <!-- 设备图标 -->
            <rect x="90" y="145" width="20" height="15" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>
            <circle cx="100" cy="152" r="2" fill="currentColor"/>
          </svg>
        </div>

        <!-- 错误信息 -->
        <div class="error-info">
          <h1 class="error-title">网络连接失败</h1>
          <p class="error-description">
            无法连接到服务器，请检查您的网络连接。<br>
            {{ getNetworkMessage() }}
          </p>
          
          <!-- 网络状态检测 -->
          <div class="network-status">
            <h3>网络状态检测：</h3>
            <div class="status-item">
              <span class="status-label">网络连接:</span>
              <span :class="['status-value', networkStatus.online ? 'status-online' : 'status-offline']">
                {{ networkStatus.online ? '已连接' : '已断开' }}
              </span>
            </div>
            <div class="status-item">
              <span class="status-label">连接类型:</span>
              <span class="status-value">{{ networkStatus.type }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">服务器状态:</span>
              <span :class="['status-value', serverStatus.reachable ? 'status-online' : 'status-offline']">
                {{ serverStatus.reachable ? '可访问' : '无法访问' }}
              </span>
            </div>
            <div v-if="!serverStatus.reachable" class="status-item">
              <span class="status-label">响应时间:</span>
              <span class="status-value">{{ serverStatus.responseTime }}ms</span>
            </div>
          </div>

          <!-- 解决方案 -->
          <div class="network-solutions">
            <h3>解决方案：</h3>
            <ul>
              <li>检查网络连接是否正常</li>
              <li>尝试刷新页面或重新加载</li>
              <li>检查WiFi或移动数据连接</li>
              <li>重启路由器或调制解调器</li>
              <li>联系网络服务提供商</li>
              <li>检查防火墙或代理设置</li>
            </ul>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="error-actions">
          <button @click="checkConnection" :disabled="isChecking" class="action-btn primary">
            <svg v-if="!isChecking" class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <svg v-else class="btn-icon spinning" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 4V10H7M23 20V14H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            {{ isChecking ? '检测中...' : '检测网络' }}
          </button>
          
          <button @click="retryConnection" :disabled="isRetrying" class="action-btn secondary">
            <svg v-if="!isRetrying" class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 4V10H7M23 20V14H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <svg v-else class="btn-icon spinning" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.3"/>
              <path d="M12 2A10 10 0 0 1 22 12" stroke="currentColor" stroke-width="4" fill="none" stroke-linecap="round"/>
            </svg>
            {{ isRetrying ? '重试中...' : '重试连接' }}
          </button>

          <router-link to="/" class="action-btn tertiary">
            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            返回首页
          </router-link>
        </div>

        <!-- 网络诊断信息 -->
        <div class="diagnostic-info">
          <h3>网络诊断建议：</h3>
          <div class="diagnostic-steps">
            <div class="diagnostic-step">
              <span class="step-number">1</span>
              <div class="step-content">
                <h4>检查基础连接</h4>
                <p>确认设备已连接到WiFi或移动网络，信号强度良好</p>
              </div>
            </div>
            <div class="diagnostic-step">
              <span class="step-number">2</span>
              <div class="step-content">
                <h4>测试其他网站</h4>
                <p>尝试访问其他网站，确认是否为全局网络问题</p>
              </div>
            </div>
            <div class="diagnostic-step">
              <span class="step-number">3</span>
              <div class="step-content">
                <h4>重启网络设备</h4>
                <p>重启路由器、调制解调器或移动设备的网络连接</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import TopNavbar from '../components/TopNavbar.vue'

const router = useRouter()
const { user, isLoggedIn, logout, updateUserState } = useAuth()

// 状态管理
const isChecking = ref(false)
const isRetrying = ref(false)

// 网络状态
const networkStatus = reactive({
  online: navigator.onLine,
  type: getConnectionType()
})

// 服务器状态
const serverStatus = reactive({
  reachable: false,
  responseTime: 0
})

// 获取连接类型
function getConnectionType() {
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
  if (connection) {
    return connection.effectiveType || connection.type || '未知'
  }
  return navigator.onLine ? '已连接' : '离线'
}

// 获取网络提示信息
const getNetworkMessage = () => {
  if (!networkStatus.online) {
    return '您的设备似乎已断开网络连接。'
  } else if (!serverStatus.reachable) {
    return '网络连接正常，但无法访问服务器。'
  }
  return '网络连接出现问题，请稍后重试。'
}

// 检测网络连接
const checkConnection = async () => {
  isChecking.value = true
  
  try {
    // 更新网络状态
    networkStatus.online = navigator.onLine
    networkStatus.type = getConnectionType()
    
    // 测试服务器连接
    const startTime = Date.now()
    try {
      const response = await fetch('/api/health', {
        method: 'GET',
        cache: 'no-cache',
        signal: AbortSignal.timeout(5000) // 5秒超时
      })
      
      const endTime = Date.now()
      serverStatus.responseTime = endTime - startTime
      serverStatus.reachable = response.ok
    } catch (error) {
      serverStatus.reachable = false
      serverStatus.responseTime = Date.now() - startTime
    }
    
    // 延迟显示结果
    await new Promise(resolve => setTimeout(resolve, 1000))
    
  } finally {
    isChecking.value = false
  }
}

// 重试连接
const retryConnection = async () => {
  isRetrying.value = true
  
  try {
    await checkConnection()
    
    if (networkStatus.online && serverStatus.reachable) {
      // 连接恢复，刷新页面
      window.location.reload()
    }
  } finally {
    isRetrying.value = false
  }
}

// 网络状态监听
const handleOnline = () => {
  networkStatus.online = true
  networkStatus.type = getConnectionType()
}

const handleOffline = () => {
  networkStatus.online = false
  networkStatus.type = '离线'
}

// 处理登出
const handleLogout = () => {
  logout()
  updateUserState()
  router.push('/')
}

// 生命周期
onMounted(() => {
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 初始检测
  checkConnection()
})

onUnmounted(() => {
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
})
</script>

<style scoped>
/* CSS变量定义 - 与平台保持一致 */
:root {
  --primary-color: #16a085;
  --primary-light: #1abc9c;
  --primary-dark: #138d75;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #bdc3c7;
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

.error-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* 导航栏现在使用共享组件 TopNavbar */

/* 错误内容区域 */
.error-main {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 100px);
  padding: 2rem;
}

.error-content {
  max-width: 600px;
  text-align: center;
  background: var(--background-white);
  border-radius: var(--radius-xl);
  padding: 3rem 2rem;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
}

.error-icon {
  width: 200px;
  height: 200px;
  margin: 0 auto 2rem;
  color: var(--primary-color);
}

.error-icon svg {
  width: 100%;
  height: 100%;
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.error-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.btn-icon {
  width: 20px;
  height: 20px;
}

.action-btn.secondary {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
  border: none;
}

.action-btn.secondary:hover {
  background: linear-gradient(135deg, #2980b9 0%, #1f618d 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.action-btn.tertiary {
  background: var(--background-light);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
}

.action-btn.tertiary:hover {
  background: var(--background-white);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

/* 网络错误特有的样式覆盖 */
.error-icon {
  color: #3498db;
}

.network-status {
  text-align: left;
  background: var(--background-light);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  margin-bottom: 2rem;
  border-left: 4px solid #3498db;
}

.network-status h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: var(--background-white);
  border-radius: var(--radius-sm);
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.status-value {
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.status-online {
  color: #27ae60;
  background: #d5f4e6;
}

.status-offline {
  color: #e74c3c;
  background: #fdeaea;
}

.network-solutions {
  text-align: left;
  background: var(--background-light);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.network-solutions h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.network-solutions ul {
  list-style: none;
  padding: 0;
}

.network-solutions li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 1.5rem;
}

.network-solutions li::before {
  content: '🔧';
  position: absolute;
  left: 0;
}

.diagnostic-info {
  background: var(--background-light);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  text-align: left;
}

.diagnostic-info h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  text-align: center;
}

.diagnostic-steps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.diagnostic-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-white);
  border-radius: var(--radius-sm);
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.step-content h4 {
  color: var(--text-primary);
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.step-content p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* 网络相关的按钮样式 */
.action-btn.primary {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
  color: white !important;
  border: none;
}

.action-btn.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #2980b9 0%, #1f618d 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* 旋转动画 */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-main {
    padding: 1rem;
  }

  .error-content {
    padding: 2rem 1.5rem;
  }

  .error-title {
    font-size: 2rem;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .action-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .diagnostic-step {
    flex-direction: column;
    text-align: center;
  }

  .step-number {
    align-self: center;
  }

  .network-status,
  .network-solutions,
  .diagnostic-info {
    padding: 1rem;
  }
}
</style>
