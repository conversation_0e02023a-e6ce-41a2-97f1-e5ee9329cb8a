import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import { authGuard, adminGuard, guestGuard, userTypeGuard, adminRedirectGuard } from './guards'
import LoginView from '../views/LoginView.vue'
import RegisterView from '../views/RegisterView.vue'
import ForgotPasswordView from '../views/ForgotPasswordView.vue'
import DashboardView from '../views/DashboardView.vue'
import AdminLoginView from '../views/AdminLoginView.vue'

import AdminDashboardView from '../views/AdminDashboardView.vue'

// 用户功能页面组件
import NutritionView from '../views/NutritionView.vue'
import MealsView from '../views/MealsView.vue'
import GoalsView from '../views/GoalsView.vue'
import ReportsView from '../views/ReportsView.vue'
import ProfileView from '../views/ProfileView.vue'
import SettingsView from '../views/SettingsView.vue'

// 错误页面组件
import Error404View from '../views/Error404View.vue'
import Error403View from '../views/Error403View.vue'
import Error500View from '../views/Error500View.vue'
import ErrorNetworkView from '../views/ErrorNetworkView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      beforeEnter: adminRedirectGuard,
      meta: {
        title: '膳食营养分析平台 - 智能营养管理'
      }
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      beforeEnter: guestGuard,
      meta: {
        title: '登录 - 膳食营养分析平台'
      }
    },
    {
      path: '/register',
      name: 'register',
      component: RegisterView,
      beforeEnter: guestGuard,
      meta: {
        title: '注册 - 膳食营养分析平台'
      }
    },
    {
      path: '/forgot-password',
      name: 'forgot-password',
      component: ForgotPasswordView,
      beforeEnter: guestGuard,
      meta: {
        title: '忘记密码 - 膳食营养分析平台'
      }
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: DashboardView,
      beforeEnter: (to, from, next) => {
        // 先检查管理员重定向，再检查用户类型
        adminRedirectGuard(to, from, (result?: any) => {
          if (result) {
            next(result)
          } else {
            userTypeGuard(['USER'])(to, from, next)
          }
        })
      },
      meta: {
        title: '控制台 - 膳食营养分析平台'
      }
    },
    {
      path: '/admin/login',
      name: 'admin-login',
      component: AdminLoginView,
      beforeEnter: guestGuard,
      meta: {
        title: '管理员登录 - 膳食营养分析平台'
      }
    },

    {
      path: '/admin/dashboard',
      name: 'admin-dashboard',
      component: AdminDashboardView,
      beforeEnter: adminGuard,
      meta: {
        title: '管理后台 - 膳食营养分析平台'
      }
    },
    // 用户功能页面路由
    {
      path: '/nutrition',
      name: 'nutrition',
      component: NutritionView,
      beforeEnter: userTypeGuard(['USER']),
      meta: {
        title: '营养分析 - 膳食营养分析平台'
      }
    },
    {
      path: '/meals',
      name: 'meals',
      component: MealsView,
      beforeEnter: userTypeGuard(['USER']),
      meta: {
        title: '膳食记录 - 膳食营养分析平台'
      }
    },
    {
      path: '/goals',
      name: 'goals',
      component: GoalsView,
      beforeEnter: userTypeGuard(['USER']),
      meta: {
        title: '健康目标 - 膳食营养分析平台'
      }
    },
    {
      path: '/reports',
      name: 'reports',
      component: ReportsView,
      beforeEnter: userTypeGuard(['USER']),
      meta: {
        title: '健康报告 - 膳食营养分析平台'
      }
    },
    {
      path: '/profile',
      name: 'profile',
      component: ProfileView,
      beforeEnter: userTypeGuard(['USER']),
      meta: {
        title: '个人资料 - 膳食营养分析平台'
      }
    },
    {
      path: '/settings',
      name: 'settings',
      component: SettingsView,
      beforeEnter: userTypeGuard(['USER']),
      meta: {
        title: '设置 - 膳食营养分析平台'
      }
    },
    // 错误页面路由
    {
      path: '/error/403',
      name: 'error-403',
      component: Error403View,
      meta: {
        title: '访问被拒绝 - 膳食营养分析平台'
      }
    },
    {
      path: '/error/500',
      name: 'error-500',
      component: Error500View,
      meta: {
        title: '服务器错误 - 膳食营养分析平台'
      }
    },
    {
      path: '/error/network',
      name: 'error-network',
      component: ErrorNetworkView,
      meta: {
        title: '网络错误 - 膳食营养分析平台'
      }
    },
    // 404页面 - 必须放在最后，作为通配符路由
    {
      path: '/:pathMatch(.*)*',
      name: 'error-404',
      component: Error404View,
      meta: {
        title: '页面未找到 - 膳食营养分析平台'
      }
    }
  ]
})

// 路由守卫 - 设置页面标题
router.beforeEach((to) => {
  if (to.meta.title) {
    document.title = to.meta.title as string
  }
})

export default router
