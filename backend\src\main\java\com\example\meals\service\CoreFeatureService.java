package com.example.meals.service;

import com.example.meals.dto.CoreFeatureRequest;
import com.example.meals.dto.CoreFeatureResponse;
import com.example.meals.dto.PageResponse;
import com.example.meals.common.Result;

import java.util.List;

/**
 * 核心功能服务接口
 */
public interface CoreFeatureService {
    
    /**
     * 创建核心功能
     */
    Result<CoreFeatureResponse> createCoreFeature(CoreFeatureRequest request);
    
    /**
     * 更新核心功能
     */
    Result<CoreFeatureResponse> updateCoreFeature(Long id, CoreFeatureRequest request);
    
    /**
     * 删除核心功能
     */
    Result<Void> deleteCoreFeature(Long id);
    
    /**
     * 根据ID获取核心功能
     */
    Result<CoreFeatureResponse> getCoreFeatureById(Long id);
    
    /**
     * 获取所有启用的核心功能（用于前端展示）
     */
    Result<List<CoreFeatureResponse>> getAllEnabledCoreFeatures();
    
    /**
     * 分页查询核心功能列表（管理员使用）
     */
    Result<PageResponse<CoreFeatureResponse>> getCoreFeatureList(
            Integer page, Integer size, String title, Integer status
    );
    
    /**
     * 更新核心功能状态
     */
    Result<Void> updateCoreFeatureStatus(Long id, Integer status);
    
    /**
     * 更新核心功能排序
     */
    Result<Void> updateCoreFeatureSortOrder(Long id, Integer sortOrder);
    
    /**
     * 批量更新排序
     */
    Result<Void> batchUpdateSortOrder(List<Long> ids);
}
