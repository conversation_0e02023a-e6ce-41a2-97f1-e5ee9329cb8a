package com.example.meals.common;

/**
 * 统一响应结果类
 */
public class Result<T> {
    
    private Integer code;    // 状态码
    private String message;  // 响应消息
    private T data;         // 响应数据
    private Boolean success; // 是否成功
    
    // 私有构造函数
    private Result() {}
    
    private Result(Integer code, String message, T data, Boolean success) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.success = success;
    }
    
    // 成功响应（无数据）
    public static <T> Result<T> success() {
        return new Result<>(200, "操作成功", null, true);
    }
    
    // 成功响应（带数据）
    public static <T> Result<T> success(T data) {
        return new Result<>(200, "操作成功", data, true);
    }
    
    // 成功响应（带消息和数据）
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(200, message, data, true);
    }
    
    // 失败响应
    public static <T> Result<T> error(String message) {
        return new Result<>(500, message, null, false);
    }
    
    // 失败响应（带状态码）
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(code, message, null, false);
    }
    
    // 参数错误
    public static <T> Result<T> badRequest(String message) {
        return new Result<>(400, message, null, false);
    }
    
    // 未授权
    public static <T> Result<T> unauthorized(String message) {
        return new Result<>(401, message, null, false);
    }
    
    // 禁止访问
    public static <T> Result<T> forbidden(String message) {
        return new Result<>(403, message, null, false);
    }
    
    // 资源不存在
    public static <T> Result<T> notFound(String message) {
        return new Result<>(404, message, null, false);
    }
    
    // Getter 和 Setter 方法
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    public Boolean getSuccess() {
        return success;
    }
    
    public void setSuccess(Boolean success) {
        this.success = success;
    }
    
    @Override
    public String toString() {
        return "Result{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", success=" + success +
                '}';
    }
}
