package com.example.meals.service.impl;

import com.example.meals.common.Result;
import com.example.meals.dto.HealthGoalResponse;
import com.example.meals.dto.request.HealthGoalRequest;
import com.example.meals.entity.HealthGoal;
import com.example.meals.mapper.HealthGoalMapper;
import com.example.meals.service.HealthGoalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 健康目标服务实现类
 */
@Service
public class HealthGoalServiceImpl implements HealthGoalService {
    
    @Autowired
    private HealthGoalMapper healthGoalMapper;
    
    @Override
    public Result<List<HealthGoalResponse>> getAllHealthGoals() {
        try {
            List<HealthGoal> goals = healthGoalMapper.selectAll();
            List<HealthGoalResponse> responses = goals.stream()
                    .map(HealthGoalResponse::new)
                    .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("获取健康目标列表失败");
        }
    }
    
    @Override
    public Result<List<HealthGoalResponse>> getEnabledHealthGoals() {
        try {
            List<HealthGoal> goals = healthGoalMapper.selectEnabled();
            List<HealthGoalResponse> responses = goals.stream()
                    .map(HealthGoalResponse::new)
                    .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("获取启用的健康目标列表失败");
        }
    }
    
    @Override
    public Result<HealthGoalResponse> getHealthGoalById(Long id) {
        try {
            HealthGoal goal = healthGoalMapper.selectById(id);
            if (goal == null) {
                return Result.notFound("健康目标不存在");
            }
            return Result.success(new HealthGoalResponse(goal));
        } catch (Exception e) {
            return Result.error("获取健康目标详情失败");
        }
    }
    
    @Override
    public Result<HealthGoalResponse> createHealthGoal(HealthGoalRequest request) {
        try {
            // 验证必填字段
            if (request.getCode() == null || request.getCode().trim().isEmpty()) {
                return Result.badRequest("目标代码不能为空");
            }
            if (request.getName() == null || request.getName().trim().isEmpty()) {
                return Result.badRequest("目标名称不能为空");
            }
            
            // 检查代码是否已存在
            if (healthGoalMapper.countByCode(request.getCode()) > 0) {
                return Result.badRequest("目标代码已存在");
            }
            
            // 创建实体
            HealthGoal goal = new HealthGoal();
            goal.setCode(request.getCode().trim());
            goal.setName(request.getName().trim());
            goal.setDescription(request.getDescription());
            goal.setSortOrder(request.getSortOrder() != null ? request.getSortOrder() : 
                             healthGoalMapper.getMaxSortOrder() + 1);
            goal.setStatus(request.getStatus() != null ? request.getStatus() : 1);
            
            // 插入数据库
            int result = healthGoalMapper.insert(goal);
            if (result > 0) {
                return Result.success(new HealthGoalResponse(goal));
            } else {
                return Result.error("创建健康目标失败");
            }
        } catch (Exception e) {
            return Result.error("创建健康目标失败");
        }
    }
    
    @Override
    public Result<HealthGoalResponse> updateHealthGoal(Long id, HealthGoalRequest request) {
        try {
            // 检查记录是否存在
            HealthGoal existing = healthGoalMapper.selectById(id);
            if (existing == null) {
                return Result.notFound("健康目标不存在");
            }
            
            // 验证必填字段
            if (request.getCode() == null || request.getCode().trim().isEmpty()) {
                return Result.badRequest("目标代码不能为空");
            }
            if (request.getName() == null || request.getName().trim().isEmpty()) {
                return Result.badRequest("目标名称不能为空");
            }
            
            // 检查代码是否已被其他记录使用
            if (healthGoalMapper.countByCodeExcludeId(request.getCode(), id) > 0) {
                return Result.badRequest("目标代码已存在");
            }
            
            // 更新实体
            existing.setCode(request.getCode().trim());
            existing.setName(request.getName().trim());
            existing.setDescription(request.getDescription());
            existing.setSortOrder(request.getSortOrder() != null ? request.getSortOrder() : existing.getSortOrder());
            existing.setStatus(request.getStatus() != null ? request.getStatus() : existing.getStatus());
            
            // 更新数据库
            int result = healthGoalMapper.update(existing);
            if (result > 0) {
                return Result.success(new HealthGoalResponse(existing));
            } else {
                return Result.error("更新健康目标失败");
            }
        } catch (Exception e) {
            return Result.error("更新健康目标失败");
        }
    }
    
    @Override
    public Result<Void> deleteHealthGoal(Long id) {
        try {
            // 检查记录是否存在
            HealthGoal existing = healthGoalMapper.selectById(id);
            if (existing == null) {
                return Result.notFound("健康目标不存在");
            }
            
            // 删除记录
            int result = healthGoalMapper.deleteById(id);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("删除健康目标失败");
            }
        } catch (Exception e) {
            return Result.error("删除健康目标失败");
        }
    }
}
