// JWT认证工具类

const TOKEN_KEY = 'auth_token'
const USER_KEY = 'user_info'

/**
 * Token管理工具
 */
export class TokenManager {

  /**
   * 保存Token
   * @param token JWT Token
   * @param rememberMe 是否记住我（true: localStorage, false: sessionStorage）
   */
  static setToken(token: string, rememberMe: boolean = false): void {
    if (rememberMe) {
      localStorage.setItem(TOKEN_KEY, token)
      // 清除sessionStorage中的token（如果存在）
      sessionStorage.removeItem(TOKEN_KEY)
    } else {
      sessionStorage.setItem(TOKEN_KEY, token)
      // 清除localStorage中的token（如果存在）
      localStorage.removeItem(TOKEN_KEY)
    }
  }

  /**
   * 获取Token
   */
  static getToken(): string | null {
    // 优先从sessionStorage获取（当前会话）
    const sessionToken = sessionStorage.getItem(TOKEN_KEY)
    if (sessionToken) {
      return sessionToken
    }
    // 然后从localStorage获取（记住我）
    return localStorage.getItem(TOKEN_KEY)
  }

  /**
   * 移除Token
   */
  static removeToken(): void {
    localStorage.removeItem(TOKEN_KEY)
    sessionStorage.removeItem(TOKEN_KEY)
  }
  
  /**
   * 检查Token是否存在
   */
  static hasToken(): boolean {
    return !!this.getToken()
  }
  
  /**
   * 获取Authorization头
   */
  static getAuthHeader(): string | null {
    const token = this.getToken()
    return token ? `Bearer ${token}` : null
  }
}

/**
 * 用户信息管理工具
 */
export class UserManager {
  
  /**
   * 保存用户信息
   * @param user 用户信息
   * @param rememberMe 是否记住我（true: localStorage, false: sessionStorage）
   */
  static setUser(user: any, rememberMe: boolean = false): void {
    const userStr = JSON.stringify(user)
    if (rememberMe) {
      localStorage.setItem(USER_KEY, userStr)
      // 清除sessionStorage中的用户信息（如果存在）
      sessionStorage.removeItem(USER_KEY)
    } else {
      sessionStorage.setItem(USER_KEY, userStr)
      // 清除localStorage中的用户信息（如果存在）
      localStorage.removeItem(USER_KEY)
    }
  }

  /**
   * 获取用户信息
   */
  static getUser(): any | null {
    // 优先从sessionStorage获取（当前会话）
    let userStr = sessionStorage.getItem(USER_KEY)
    if (!userStr) {
      // 然后从localStorage获取（记住我）
      userStr = localStorage.getItem(USER_KEY)
    }

    if (userStr) {
      try {
        return JSON.parse(userStr)
      } catch (error) {
        // 解析用户数据失败时清除数据
        this.removeUser()
        return null
      }
    }
    return null
  }

  /**
   * 移除用户信息
   */
  static removeUser(): void {
    localStorage.removeItem(USER_KEY)
    sessionStorage.removeItem(USER_KEY)
  }
  
  /**
   * 检查用户是否已登录
   */
  static isLoggedIn(): boolean {
    return !!this.getUser() && TokenManager.hasToken()
  }
  
  /**
   * 获取用户类型
   */
  static getUserType(): string | null {
    const user = this.getUser()
    return user?.userType || null
  }
  
  /**
   * 检查是否为管理员
   */
  static isAdmin(): boolean {
    return this.getUserType() === 'ADMIN'
  }
  
  /**
   * 检查是否为普通用户
   */
  static isUser(): boolean {
    return this.getUserType() === 'USER'
  }
}

/**
 * 认证管理器
 */
export class AuthManager {
  
  /**
   * 登录
   * @param token JWT Token
   * @param user 用户信息
   * @param rememberMe 是否记住我
   */
  static login(token: string, user: any, rememberMe: boolean = false): void {
    TokenManager.setToken(token, rememberMe)
    UserManager.setUser(user, rememberMe)
  }
  
  /**
   * 登出
   */
  static logout(): void {
    TokenManager.removeToken()
    UserManager.removeUser()
  }
  
  /**
   * 检查认证状态
   */
  static isAuthenticated(): boolean {
    return UserManager.isLoggedIn()
  }
  
  /**
   * 获取当前用户
   */
  static getCurrentUser(): any | null {
    return UserManager.getUser()
  }
  
  /**
   * 获取认证头
   */
  static getAuthHeader(): string | null {
    return TokenManager.getAuthHeader()
  }

  /**
   * 检查当前登录是否为"记住我"模式
   */
  static isRemembered(): boolean {
    // 如果localStorage中有token，说明是记住我模式
    const localToken = localStorage.getItem(TOKEN_KEY)
    return !!localToken
  }

  /**
   * 获取Token存储类型
   */
  static getTokenStorageType(): 'session' | 'persistent' | null {
    const sessionToken = sessionStorage.getItem(TOKEN_KEY)
    const localToken = localStorage.getItem(TOKEN_KEY)

    if (sessionToken) return 'session'
    if (localToken) return 'persistent'
    return null
  }

  /**
   * 初始化认证状态（应用启动时调用）
   * 检查Token有效性，清理过期或无效的Token
   */
  static initializeAuth(): void {
    const token = TokenManager.getToken()
    if (!token) {
      return
    }

    // 这里可以添加Token有效性检查逻辑
    // 如果Token无效，清理存储
    try {
      // 简单检查Token格式（实际项目中可能需要向服务器验证）
      const parts = token.split('.')
      if (parts.length !== 3) {
        // Token格式无效，清理
        this.logout()
        return
      }

      // 检查Token是否过期（解析JWT payload）
      const payload = JSON.parse(atob(parts[1]))
      const currentTime = Math.floor(Date.now() / 1000)

      if (payload.exp && payload.exp < currentTime) {
        // Token已过期，清理
        this.logout()
        return
      }
    } catch (error) {
      // Token解析失败，清理
      this.logout()
    }
  }
}

/**
 * HTTP请求拦截器
 */
export function createAuthenticatedRequest() {
  return async function authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
    const authHeader = AuthManager.getAuthHeader()

    const headers: Record<string, string> = {
      ...options.headers as Record<string, string>,
    }

    // 只有当请求体不是FormData时才设置Content-Type为application/json
    // FormData需要让浏览器自动设置multipart/form-data和boundary
    if (!(options.body instanceof FormData)) {
      headers['Content-Type'] = 'application/json'
    }

    if (authHeader) {
      headers['Authorization'] = authHeader
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      })

      // 如果返回401，说明token过期或无效，自动登出
      if (response.status === 401) {
        AuthManager.logout()
        // 重定向到登录页面
        window.location.href = '/login'
        return response
      }

      // 如果返回403，重定向到权限错误页面
      if (response.status === 403) {
        window.location.href = '/error/403'
        return response
      }

      // 如果返回5xx错误，重定向到服务器错误页面
      if (response.status >= 500) {
        window.location.href = '/error/500'
        return response
      }

      return response
    } catch (error) {
      // 网络错误处理
      if (error instanceof TypeError && error.message.includes('fetch')) {
        window.location.href = '/error/network'
      }
      throw error
    }
  }
}

// 创建认证请求实例
export const authFetch = createAuthenticatedRequest()

/**
 * API响应处理工具
 */
export async function handleApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const result = await response.json()

  if (!result.success) {
    throw new Error(result.message || 'API request failed')
  }

  return result.data
}

/**
 * API响应处理工具（返回完整响应）
 */
export async function handleFullApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const result = await response.json()
  return result
}

/**
 * 带认证的API请求工具
 */
export async function apiRequest<T>(url: string, options: RequestInit = {}): Promise<T> {
  const response = await authFetch(url, options)
  return handleApiResponse<T>(response)
}

/**
 * 带认证的API请求工具（返回完整响应）
 */
export async function apiRequestFull<T>(url: string, options: RequestInit = {}): Promise<T> {
  const response = await authFetch(url, options)
  return handleFullApiResponse<T>(response)
}
