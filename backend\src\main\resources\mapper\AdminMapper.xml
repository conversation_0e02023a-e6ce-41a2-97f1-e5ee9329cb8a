<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.AdminMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.meals.entity.Admin">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, username, password, email, real_name, avatar, status, create_time, update_time, last_login_time
    </sql>

    <!-- 根据ID查询管理员 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM admin
        WHERE id = #{id}
    </select>

    <!-- 根据用户名查询管理员 -->
    <select id="selectByUsername" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM admin
        WHERE username = #{username}
    </select>

    <!-- 根据邮箱查询管理员 -->
    <select id="selectByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM admin
        WHERE email = #{email}
    </select>

    <!-- 查询所有管理员（分页） -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM admin
        ORDER BY create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 根据条件查询管理员 -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM admin
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="realName != null and realName != ''">
                AND real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="role != null">
                AND role = #{role}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 统计管理员总数 -->
    <select id="countAll" resultType="int">
        SELECT COUNT(*) FROM admin
    </select>

    <!-- 根据条件统计管理员数量 -->
    <select id="countByCondition" resultType="int">
        SELECT COUNT(*) FROM admin
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="realName != null and realName != ''">
                AND real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="role != null">
                AND role = #{role}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

    <!-- 插入管理员 -->
    <insert id="insert" parameterType="com.example.meals.entity.Admin" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO admin (username, password, email, real_name, avatar, status, create_time, update_time)
        VALUES (#{username}, #{password}, #{email}, #{realName}, #{avatar}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <!-- 更新管理员信息 -->
    <update id="updateById" parameterType="com.example.meals.entity.Admin">
        UPDATE admin
        <set>
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="email != null">email = #{email},</if>
            <if test="realName != null">real_name = #{realName},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="status != null">status = #{status},</if>
            update_time = #{updateTime}
        </set>
        WHERE id = #{id}
    </update>

    <!-- 更新最后登录时间 -->
    <update id="updateLastLoginTime">
        UPDATE admin SET last_login_time = #{lastLoginTime} WHERE id = #{id}
    </update>

    <!-- 根据ID删除管理员 -->
    <delete id="deleteById">
        DELETE FROM admin WHERE id = #{id}
    </delete>

    <!-- 检查用户名是否存在 -->
    <select id="countByUsername" resultType="int">
        SELECT COUNT(*) FROM admin WHERE username = #{username}
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="countByEmail" resultType="int">
        SELECT COUNT(*) FROM admin WHERE email = #{email}
    </select>

</mapper>
