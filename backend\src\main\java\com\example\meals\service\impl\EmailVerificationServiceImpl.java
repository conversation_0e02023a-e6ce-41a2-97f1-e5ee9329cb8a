package com.example.meals.service.impl;

import com.example.meals.common.Result;
import com.example.meals.entity.EmailVerification;
import com.example.meals.entity.User;
import com.example.meals.mapper.EmailVerificationMapper;
import com.example.meals.mapper.UserMapper;
import com.example.meals.service.EmailService;
import com.example.meals.service.EmailVerificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Random;

/**
 * 邮箱验证码服务实现类
 */
@Service
public class EmailVerificationServiceImpl implements EmailVerificationService {
    
    @Autowired
    private EmailVerificationMapper emailVerificationMapper;

    @Autowired
    private EmailService emailService;

    @Autowired
    private UserMapper userMapper;
    
    // 验证码有效期（分钟）
    private static final int CODE_EXPIRE_MINUTES = 5;
    
    // 发送间隔限制（秒）
    private static final int SEND_INTERVAL_SECONDS = 60;
    
    // 每小时最大发送次数
    private static final int MAX_SEND_PER_HOUR = 10;
    
    @Override
    public Result<Void> sendVerificationCode(String email, String type) {
        // 参数验证
        if (!StringUtils.hasText(email)) {
            return Result.badRequest("邮箱地址不能为空");
        }
        if (!StringUtils.hasText(type)) {
            return Result.badRequest("验证类型不能为空");
        }
        
        // 对于忘记密码类型，检查邮箱是否已注册
        if ("forgot_password".equals(type)) {
            User existingUser = userMapper.selectByEmail(email);
            if (existingUser == null) {
                return Result.badRequest("邮箱未注册");
            }
        }

        // 检查发送限制
        Result<Integer> limitCheck = checkSendLimit(email, type);
        if (!limitCheck.getSuccess()) {
            return Result.error(limitCheck.getMessage());
        }
        if (limitCheck.getData() > 0) {
            return Result.error("发送过于频繁，请等待 " + limitCheck.getData() + " 秒后再试");
        }
        
        try {
            // 生成6位数字验证码
            String verificationCode = generateVerificationCode();
            
            // 计算过期时间
            LocalDateTime expireTime = LocalDateTime.now().plusMinutes(CODE_EXPIRE_MINUTES);
            
            // 创建验证码记录
            EmailVerification emailVerification = new EmailVerification(email, verificationCode, type, expireTime);
            
            // 保存到数据库
            int result = emailVerificationMapper.insert(emailVerification);
            if (result <= 0) {
                return Result.error("验证码生成失败，请重试");
            }
            
            // 发送邮件
            Result<Void> emailResult = emailService.sendVerificationCode(email, verificationCode, type);
            if (!emailResult.getSuccess()) {
                return Result.error("验证码邮件发送失败：" + emailResult.getMessage());
            }
            
            return Result.success();
            
        } catch (Exception e) {
            return Result.error("发送验证码失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> verifyCode(String email, String code, String type) {
        // 参数验证
        if (!StringUtils.hasText(email)) {
            return Result.badRequest("邮箱地址不能为空");
        }
        if (!StringUtils.hasText(code)) {
            return Result.badRequest("验证码不能为空");
        }
        if (!StringUtils.hasText(type)) {
            return Result.badRequest("验证类型不能为空");
        }
        
        try {
            // 查找验证码记录
            EmailVerification verification = emailVerificationMapper.findByEmailAndCode(email, code, type);
            if (verification == null) {
                return Result.badRequest("验证码错误或已失效");
            }
            
            // 检查是否已使用
            if (verification.getStatus() != 0) {
                return Result.badRequest("验证码已使用或已过期");
            }
            
            // 检查是否过期
            if (verification.getExpireTime().isBefore(LocalDateTime.now())) {
                // 标记为过期
                emailVerificationMapper.updateStatus(verification.getId(), 2, null);
                return Result.badRequest("验证码已过期，请重新获取");
            }
            
            // 标记为已使用
            emailVerificationMapper.updateStatus(verification.getId(), 1, LocalDateTime.now());
            
            // 使同一邮箱同一类型的其他验证码失效
            emailVerificationMapper.invalidateCodesByEmailAndType(email, type);
            
            return Result.success();
            
        } catch (Exception e) {
            return Result.error("验证码验证失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Boolean> checkCode(String email, String code, String type) {
        try {
            EmailVerification verification = emailVerificationMapper.findByEmailAndCode(email, code, type);
            if (verification == null) {
                return Result.success(false);
            }
            
            // 检查状态和过期时间
            boolean isValid = verification.getStatus() == 0 && 
                            verification.getExpireTime().isAfter(LocalDateTime.now());
            
            return Result.success(isValid);
            
        } catch (Exception e) {
            return Result.error("检查验证码失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> invalidateCodes(String email, String type) {
        try {
            emailVerificationMapper.invalidateCodesByEmailAndType(email, type);
            return Result.success();
        } catch (Exception e) {
            return Result.error("使验证码失效失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Integer> cleanExpiredCodes() {
        try {
            // 清理7天前的记录
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(7);
            int deletedCount = emailVerificationMapper.deleteExpiredCodes(cutoffTime);
            return Result.success("清理完成", deletedCount);
        } catch (Exception e) {
            return Result.error("清理过期验证码失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Integer> checkSendLimit(String email, String type) {
        try {
            LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
            LocalDateTime oneMinuteAgo = LocalDateTime.now().minusSeconds(SEND_INTERVAL_SECONDS);
            
            // 检查1小时内发送次数
            int hourlyCount = emailVerificationMapper.countRecentCodes(email, type, oneHourAgo);
            if (hourlyCount >= MAX_SEND_PER_HOUR) {
                return Result.error("发送次数过多，请1小时后再试");
            }
            
            // 检查最近1分钟内是否发送过
            int recentCount = emailVerificationMapper.countRecentCodes(email, type, oneMinuteAgo);
            if (recentCount > 0) {
                // 计算剩余等待时间
                EmailVerification latest = emailVerificationMapper.findLatestValidCode(email, type);
                if (latest != null) {
                    LocalDateTime nextAllowTime = latest.getCreateTime().plusSeconds(SEND_INTERVAL_SECONDS);
                    if (nextAllowTime.isAfter(LocalDateTime.now())) {
                        long waitSeconds = java.time.Duration.between(LocalDateTime.now(), nextAllowTime).getSeconds();
                        return Result.success((int) waitSeconds);
                    }
                }
            }
            
            return Result.success(0); // 可以发送
            
        } catch (Exception e) {
            return Result.error("检查发送限制失败：" + e.getMessage());
        }
    }
    
    /**
     * 生成6位数字验证码
     */
    private String generateVerificationCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }
}
