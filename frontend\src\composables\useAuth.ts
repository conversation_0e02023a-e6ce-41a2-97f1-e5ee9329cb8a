import { ref, computed, watch } from 'vue'
import type { User } from '../utils/userApi'
import { AuthManager, UserManager } from '../utils/auth'

// 全局用户状态
const user = ref<User | null>(null)
const isLoggedIn = computed(() => !!user.value)

// 初始化全局状态
const initializeGlobalState = () => {
  const savedUser = UserManager.getUser()
  if (savedUser && AuthManager.isAuthenticated()) {
    user.value = savedUser
  } else {
    user.value = null
  }
}

// 立即初始化
initializeGlobalState()

// 用户认证相关的composable
export function useAuth() {
  
  // 设置用户信息（已废弃，使用AuthManager.login）
  const setUser = (userData: User) => {
    user.value = userData
    UserManager.setUser(userData)
  }

  // 更新用户状态（用于响应AuthManager的变化）
  const updateUserState = () => {
    initializeGlobalState()
  }

  // 清除用户信息（已废弃，使用AuthManager.logout）
  const clearUser = () => {
    user.value = null
    AuthManager.logout()
  }

  // 初始化用户信息（从localStorage恢复）
  const initUser = () => {
    updateUserState()
  }

  // 登出
  const logout = () => {
    user.value = null
    AuthManager.logout()
    // 确保状态立即更新
    initializeGlobalState()
  }

  // 检查是否为管理员
  const isAdmin = computed(() => UserManager.isAdmin())

  // 检查是否为普通用户
  const isUser = computed(() => UserManager.isUser())

  // 获取用户类型
  const userType = computed(() => UserManager.getUserType())

  return {
    user: computed(() => user.value),
    isLoggedIn,
    isAdmin,
    isUser,
    userType,
    setUser,
    clearUser,
    initUser,
    logout,
    updateUserState
  }
}
