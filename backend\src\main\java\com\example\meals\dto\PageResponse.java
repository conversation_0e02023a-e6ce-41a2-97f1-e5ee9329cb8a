package com.example.meals.dto;

import java.util.List;

/**
 * 分页响应DTO
 */
public class PageResponse<T> {
    
    private List<T> records;    // 数据列表
    private Long total;         // 总记录数
    private Integer page;       // 当前页码
    private Integer size;       // 每页大小
    private Integer pages;      // 总页数
    
    // 构造函数
    public PageResponse() {}
    
    public PageResponse(List<T> records, Long total, Integer page, Integer size) {
        this.records = records;
        this.total = total;
        this.page = page;
        this.size = size;
        this.pages = (int) Math.ceil((double) total / size);
    }
    
    // 静态工厂方法
    public static <T> PageResponse<T> of(List<T> records, Long total, Integer page, Integer size) {
        return new PageResponse<>(records, total, page, size);
    }
    
    // Getter 和 Setter 方法
    public List<T> getRecords() {
        return records;
    }
    
    public void setRecords(List<T> records) {
        this.records = records;
    }
    
    public Long getTotal() {
        return total;
    }
    
    public void setTotal(Long total) {
        this.total = total;
    }
    
    public Integer getPage() {
        return page;
    }
    
    public void setPage(Integer page) {
        this.page = page;
    }
    
    public Integer getSize() {
        return size;
    }
    
    public void setSize(Integer size) {
        this.size = size;
    }
    
    public Integer getPages() {
        return pages;
    }
    
    public void setPages(Integer pages) {
        this.pages = pages;
    }
    
    @Override
    public String toString() {
        return "PageResponse{" +
                "records=" + records +
                ", total=" + total +
                ", page=" + page +
                ", size=" + size +
                ", pages=" + pages +
                '}';
    }
}
